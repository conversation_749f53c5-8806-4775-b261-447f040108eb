/**
 *  @module Mixin:Test
 */

module.exports = function(Model) {

	Model.test = async function(id) {
		const { Person } = Model.app.models,
			last = new Date(),
			objects = await Person.syncFetch(id, last)

		return objects
	}

	Model.remoteMethod('test', {
		description: 'Test API',
		http: { path: '/test', verb: 'post' },
		accepts: [
			{
				arg: 'id',
				type: 'string',
				required: true
			},
		],
		returns: { type: 'any', root: true },
	})
}
