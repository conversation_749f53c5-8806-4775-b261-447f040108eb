{"initial:before": {"loopback#favicon": {}}, "initial": {"compression": {}, "cors": {"params": {"origin": true, "credentials": true, "maxAge": 86400}}}, "session": {}, "auth": {}, "auth:after": {"./lib/common/middleware/multitenant": {"name": "multitenant", "paths": ["/api/Cache", "/api/AppEvents/app"], "params": {"tenant-code": "trap"}, "enabled": true}, "./lib/common/middleware/install": {"name": "install", "paths": ["/api/Cache/app", "/api/AppEvents/app"]}}, "parse": {"body-parser#json": {"params": {"limit": "20mb"}}, "body-parser#urlencoded": {"params": {"limit": "1mb", "extended": true}}}, "routes": {"loopback#rest": {}}, "files": {}, "final": {"loopback#urlNotFound": {}}, "final:after": {"./lib/common/middleware/error-handler": {"name": "error-handler", "paths": ["${restApiRoot}"], "enabled": true}, "strong-error-handler": {"params": {"debug": true, "log": true, "safeFields": ["code", "details"]}}}}