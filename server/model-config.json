{"_meta": {"sources": ["loopback/common/models", "loopback/server/models", "../common/models", "./lib/common/models", "./lib/perkd/models", "./models"], "mixins": ["loopback/common/mixins", "loopback/server/mixins", "../common/mixins", "./lib/common/mixins", "./lib/perkd/mixins", "./mixins"]}, "User": {"dataSource": "db", "public": false}, "AccessToken": {"dataSource": "db", "public": false}, "ACL": {"dataSource": "db", "public": false}, "RoleMapping": {"dataSource": "db", "public": false}, "Role": {"dataSource": "db", "public": false}, "Service": {"dataSource": "db", "public": true}, "Locale": {"dataSource": "transient", "public": false}, "Geometry": {"dataSource": "transient", "public": false}, "Permission": {"dataSource": "transient", "public": false}, "AppEvent": {"dataSource": "transient", "public": true}, "Cache": {"dataSource": "transient", "public": true}, "Account": {"dataSource": "accountRemote", "public": false}, "Action": {"dataSource": "actionRemote", "public": false}, "AccountSetting": {"dataSource": "accountRemote", "public": false}, "Person": {"dataSource": "person<PERSON><PERSON><PERSON>", "public": false}, "Card": {"dataSource": "cardRemote", "public": false}, "Installation": {"dataSource": "installationRemote", "public": false}, "Offer": {"dataSource": "offerRemote", "public": false}, "Reward": {"dataSource": "rewardRemote", "public": false}, "Message": {"dataSource": "richMessageRemote", "public": false}, "Place": {"dataSource": "placeRemote", "public": false}, "WidgetData": {"dataSource": "widgetRemote", "public": false}}