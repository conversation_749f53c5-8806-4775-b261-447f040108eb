{"name": "AppEvent", "plural": "AppEvents", "base": "PersistedModel", "description": "", "strictObjectIDCoercion": true, "forceId": false, "mixins": {"Timestamp": true, "Errors": true, "Queue": true, "Sync": true, "CacheEvents": {"service": "sync"}, "DisableAllRemotes": {"find": true, "count": true}}, "properties": {"object": {"type": "String"}, "action": {"type": "String"}, "install": {"type": "Object"}, "location": {"type": "Object"}, "context": {"type": "Object"}, "cardMaster": {"type": "Object"}, "card": {"type": "Object"}, "offer": {"type": "Object"}, "reward": {"type": "Object"}, "widget": {"type": "Object"}, "perkdCode": {"type": "Object"}, "paymentMethod": {"type": "Object"}, "occurredAt": {"type": "Date"}, "createdAt": {"type": "Date"}, "modifiedAt": {"type": "Date"}, "deletedAt": {"type": "Date"}}, "settings": {}, "validations": [], "relations": {"person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}}, "acls": [], "indexes": {"object": {"keys": {"object": 1}}, "action": {"keys": {"action": 1}}, "install.id": {"keys": {"install.id": 1}}, "occurredAt": {"keys": {"occurredAt": -1}}, "createdAt": {"keys": {"createdAt": -1}}, "modifiedAt": {"keys": {"modifiedAt": -1}}, "deletedAt": {"keys": {"deletedAt": -1}}, "personId": {"keys": {"personId": 1}}}, "scopes": {}, "methods": {}}