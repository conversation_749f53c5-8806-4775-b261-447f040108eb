{"name": "<PERSON><PERSON>", "plural": "<PERSON><PERSON>", "description": "", "base": "Model", "idInjection": true, "strict": true, "mixins": {"Queue": true, "DisableAllRemotes": {}}, "properties": {}, "hidden": [], "settings": {}, "validations": [], "relations": {}, "acls": [], "scopes": {}, "indexes": {}, "methods": {"sync": {"description": "Sync (App API)", "http": {"path": "/app/sync", "verb": "post"}, "accepts": [{"arg": "objectsUp", "type": "array", "required": true}, {"arg": "syncUntil", "type": "number"}, {"arg": "limit", "type": "number"}, {"arg": "background", "type": "boolean"}, {"arg": "options", "type": "object", "description": "backward compatible: { syncUntil, fetchLimit }"}, {"arg": "req", "type": "object", "http": {"source": "req"}}, {"arg": "res", "type": "object", "http": {"source": "res"}}], "returns": {"type": "object", "root": true, "description": "{ objects: [], syncUntil, partial }"}}}}