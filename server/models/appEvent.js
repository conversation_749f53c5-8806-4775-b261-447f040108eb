/**
 *  @module Model:AppEvent
 */

const { Wallet } = require('@crm/types'),
	{ Installations, AppEvents } = Wallet,
	{ ONLINE, OFFLINE, SUSPEND } = Installations.State,
	{ APP, SHOP } = AppEvents.Object,
	{ PAUSE, RESUME, UPGRADED } = AppEvents.Action,
	DIFF = 500,			// in ms, minimum diff between 2 continuous & same app events' occurredAt
	THRESHOLD = 100,	// threshold of same events count within 1 sync up, to trigger warning
	concurrency = 10 	// update installation when pause resume

class Event {

	constructor(app, obj, install) {
		const { id, object, action, occurredAt } = obj,
			{ personId } = install

		this.app = app
		this.id = id
		this.object = object
		this.action = action
		this.occurredAt = occurredAt
		this.personId = personId

		Object.defineProperty(this, '_source', { value: obj })
		Object.defineProperty(this, '_install', { value: install })
		Object.defineProperty(this, '_personId', { value: personId })

		Object.defineProperty(this, '$', { value: {} })
		// this._errors = [];
		Object.defineProperty(this, '_factory', {
			value: {
				// log: () => this.data(),
				app: () =>
					this.common()
						.context()
						.pause()
						.resume()
						.metrics(),
				bag: () =>
					this.common()
						.context()
						.bag()
						.items()
						.cardMaster()
						.card()
						.message()
						.tags(),
				card: () =>
					this.common()
						.cardMaster()
						.card()
						.context(),
				// .accept()
				// .decline(),
				// dialog: () =>
				// 	this.common()
				// 		.context(),
				discover: () =>
					this.common()
						.cardMaster()
						// .widget()
						.context(),
				engage: () =>
					this.common()
						.card()
						.cardMaster()
						.offer()
						.reward()
						.context(),
				// interaction: () =>
				// 	this.common()
				// 		.context(),
				location: () =>
					this.common()
						.context(),
				message: () =>
					this.common()
						.cardMaster()
						.card()
						.message()
						// .perkdCode()
						.context(),
				notify: () =>
					this.common()
						.context(),
				offer: () =>
					this.common()
						.cardMaster()
						.card()
						.offer()
						.context(),
				payment: () =>
					this.common()
						.paymentMethod()
						.context(),
				// pbox: () =>
				// 	this.common()
				// 		.context(),
				permission: () =>
					this.common()
						.context(),
				perkdcode: () =>
					this.common()
						.perkdCode()
						.context(),
				person: () =>
					this.common()
						.context(),
				place: () =>
					this.common()
						.cardMaster()
						.card()
						.place()
						.context(),
				// push: () =>
				// 	this.common()
				// 		.context(),
				reward: () =>
					this.common()
						.cardMaster()
						.card()
						.reward()
						.context(),
				scan: () =>
					this.common()
						.actions()
						.context(),
				shop: () =>
					this.common()
						.context()
						.product()
						.brand()
						.merchant()
						.metrics(),
				sync: () =>
					this.common()
						.context(),
				urlscheme: () =>
					this.common()
						.actions(),
				widget: () =>
					this.common()
						.cardMaster()
						.card()
						.widget()
						// .perkdCode()
						.context(),
				rating: () =>
					this.common()
						.context()
						.cardMaster()
						.card()
			},
		})
		this._factory[object] && this._factory[object]()
	}

	toJSON() {
		const { id, object, action, $ } = this
		return Object.assign({ id, object, action }, $)
	}

	// ----  Objects (base) ----

	common() {
		const { personId, occurredAt, _install, _source } = this,
			{ id, app, device, os } = _install ?? {}

		this.$.personId = personId
		this.$.occurredAt = occurredAt
		this.$.install = { id, app, device, os }
		this.$.location = _source.location || undefined
		return this
	}

	context() {
		this.$.context = this._source.data.context
		return this
	}

	cardMaster() {
		this.$.cardMaster = this._source.data.cardMaster
		return this
	}

	card() {
		this.$.card = this._source.data.card
		return this
	}

	merchant() {
		this.$.merchant = this._source.data.merchant
		return this
	}

	paymentMethod() {
		const { data } = this._source
		this.$.paymentMethod = data.paymentMethod || data.method
		return this
	}

	offer() {
		this.$.offer = this._source.data.offer
		return this
	}

	reward() {
		this.$.reward = this._source.data.reward
		return this
	}

	message() {
		this.$.message = this._source.data.message
		return this
	}

	place() {
		this.$.place = this._source.data.place
		return this
	}

	perkdCode() {
		this.$.perkdCode = this._source.data.perkdCode
		return this
	}

	product() {
		this.$.product = this._source.data.product
		return this
	}

	bag() {
		this.$.bag = this._source.data.bag
		return this
	}

	brand() {
		this.$.brand = this._source.data.brand
		return this
	}

	items() {
		this.$.items = this._source.data.items
		return this
	}

	widget() {
		this.$.widget = this._source.data.widget
		return this
	}

	actions() {
		this.$.actions = this._source.data.actions
		return this
	}

	tags() {
		this.$.tags = this._source.data.tags
		return this
	}

	// ----  Actions ----

	data() {
		this.$.data = this._source.data
		return this
	}

	pause() {
		const { app, action, _install } = this,
			{ AppEvent, Installation } = app.models,
			NOW = new Date()

		if (action === PAUSE) {
			const install = setSuspend(_install, NOW)

			AppEvent.queue('installation', Installation.upsert.bind(Installation, install), { concurrency })
				.catch(error => console.error('[AppEvent]pause', error))
		}
		return this
	}

	resume() {
		const { app, action, _install } = this,
			{ AppEvent, Installation } = app.models,
			NOW = new Date()

		if (action === RESUME) {
			const install = setOnline(_install, NOW)

			AppEvent.queue('installation', Installation.upsert.bind(Installation, install), { concurrency })
				.catch(error => console.error('[AppEvent]resume', error))
		}
		return this
	}

	metrics() {
		const { object, action, _install, _source } = this,
			{ Metric } = this.app,
			{ context, brand, merchant, product } = _source.data,
			{ app, os } = _install,
			tags = { os: os?.name, app: app?.version }

		if (object === SHOP) {
			switch (action) {
			case 'brand.view':
				appMetric(Metric.app.view.shop, 1, { child: 'brand', tags: { ...tags, merchantId: brand.merchantId, source: context.source, favorite: brand.favorite } })
				break
			case 'link.view':
				appMetric(Metric.app.view.shop, 1, { child: 'link', tags: { ...tags, merchantId: merchant.id, source: context.source, brand: product.brand } })
				break
			default:
				break
			}
		}

		if (object === APP && action === UPGRADED) {
			appMetric(Metric.app.upgraded, 1, { tags: { os: os.name, from: context.from, to: context.to } })
		}
		return this
	}

	// accept() {
	// 	if (this.action === 'accept') {
	// 		this.app.models.Card.accept(this.$.cardId, { at: this.occurredAt });
	// 	}
	// 	return this;
	// }

	// decline() {
	// 	if (this.action === 'decline') {
	// 		this.app.models.Card.decline(this.$.cardId, { at: this.occurredAt });
	// 	}
	// 	return this;
	// }
}

function setOffline(installation, at) {
	const install = { ...installation }
	install.state = OFFLINE
	return refresh(install, at)
}

function setSuspend(installation, at) {
	const install = { ...installation }
	install.state = SUSPEND
	return refresh(install, at)
}

function setOnline(installation, at) {
	const install = { ...installation }
	install.state = ONLINE
	return refresh(install, at)
}

function refresh(install, at = new Date()) {
	install.stateSince = at
	install.lastSeenAt = at
	return install
}

module.exports = function(AppEvent) {

	/**
	 * Sync API (app)
	 * @param	{Object} objectsUp - Objects to sync up to server
	 * @param	{Object} install - Installation
	 * @param	{Object} user - { personId }
	 * @return	{Promise<Null>}
	 */
	AppEvent.syncUpdate = async function(objectsUp, install, user) {
		const { app } = AppEvent,
			{ domain } = app.service,
			{ events, receipts } = prepEvents(objectsUp)

		if (Object.keys(receipts).some(evt => receipts[evt].count > THRESHOLD)) {
			appNotify('[AppEvent.syncUpdate] abnormal events count:', { receipts }, 'error')
		}

		for (let i = 0; i < events.length; i++) {
			const obj = events[i]
			if (!obj) continue

			const evt = new Event(app, obj, install),
				{ object, action } = evt,
				evtName = `${domain}.app.${object}.${action}`,
				evtData = evt.toJSON()

			app.emit(evtName, evtData)
		}
	}
}

function prepEvents(objectsUp = []) {
	return objectsUp.reduce((res, obj) => {
		if (!obj) return res

		const { object, action, data, occurredAt } = obj,
			{ id } = data?.[object] || {},
			key = `${object}.${action}${id ? `.${id}` : ''}`

		res.receipts[key] = res.receipts[key] || { time: 0, count: 0 }

		const at = new Date(occurredAt),
			diff = at - res.receipts[key].time

		res.receipts[key].time = at
		res.receipts[key].count += 1

		if (diff > DIFF) res.events.push(obj)

		return res
	}, { events: [], receipts: {} })
}
