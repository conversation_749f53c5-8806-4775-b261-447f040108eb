/**
 *  @module Model:Cache
 */
const { Touchpoints, Modules } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ isPre7app, updatesFromChanges, syncUntilOf } = require('@perkd/sync')

const { PERKD } = Touchpoints.Type,
	{ CACHE } = Touchpoints.Format,
	{ INSTALLATION } = Touchpoints.Instrument,
	{ SYNC } = Modules.Type

module.exports = function(Cache) {
	/**
	 * Sync API (app)
	 * @param	{Object[]} objectsUp
	 * @param	{Number} syncUntil
	 * @param	{Number} [limit]
	 * @param	{Boolean} [background]
	 * @param	{Object} options - backward compatible (pre 7.0)
	 *          {Number} fetchLimit - max. number of objests to sync down
	 *          {Number} syncUntil - override cache sync stat
	 * @param	{Request} req
	 * @param	{Response} res
	 * @return	{Object} { objects: [], syncUntil: timestamp(ms), partial }
	 */
	Cache.sync = async function(objectsUp = [], syncUntil, limit, background, options = {}, req, res) {
		const last = syncUntil || options.syncUntil,
			max = limit || options.fetchLimit,
			{ user, installation } = Context,
			{ personId } = user,
			{ id: deviceId } = installation,
			legacy = isPre7app(installation)

		if (!personId || !deviceId) {
			appNotify('[Cache]sync - missing personId or deviceId', { personId, deviceId, objectsUp })
			return
		}

		if (legacy) {	// FIXME compability: pre 7.0, convert Preference to AccountSetting
			const index = objectsUp.findIndex(o => o._model === 'Preference')
			if (index !== -1) objectsUp[index]._model = 'AccountSetting'
		}

		const updates = await Cache.syncUpdate(objectsUp, personId, deviceId)
		// TODO queue requests by personId?

		await Cache.syncFetch(personId, last, max, installation, updates, res)
	}

	/**
	 * Combine & apply changes from app
	 * @param	{Object[]} objectsUp
	 * @param	{String} personId
	 * @param	{String} deviceId
	 * @return	{Promise<Object[]>} objects to sync down to app
	 */
	Cache.syncUpdate = async function (objectsUp, personId, deviceId) {
		const { sync } = appModule(SYNC),
			{ models } = Cache.app,
			changes = await sync.changes.getAll(personId, deviceId, true).catch(() => ({})),
			exclude = [],
			updated = []

		for (const { _model: name, ...update } of objectsUp) {
			const { id, createdAt, modifiedAt } = update,
				modified = !!modifiedAt,
				created = !modified && !!createdAt,
				Model = models[name],
				serverChanges = changes[name.toLowerCase()]?.find(c => c.id === id)?.changes

			if (!Model) {
				console.error(`[syncUpdate] invalid model: '${name}', skipped`, { personId, deviceId, objectsUp })
				continue
			}

			update.through = {
				type: PERKD,
				format: CACHE,
				instrument: {
					type: INSTALLATION,
					id: deviceId
				},
				touchedAt: new Date(),
			}

			if (created) {
				try {
					if (id) {
						Model.create(update).catch(err => appNotify(`[${name}]syncUpdate.create`, { err, personId, update }, 'error'))
						exclude.push(id)
					}
					else {	// special case for WidgetData, created without id
						const instance = await Model.create(update)
						updated.push(instance)
					}
				}
				catch (err) {
					appNotify(`[${name}]syncUpdate.create`, { err, personId, update }, 'error')
					exclude.push(id)
				}
			}
			else if (!serverChanges?.length) {
				// only app changes
				try {
					if (id) {
						Model.upsert(update).catch(err => appNotify(`[${name}]syncUpdate.upsert`, { err, personId, update }, 'error'))
						exclude.push(id)
					}
					else {	// special case for WidgetData, created without id
						const instance = await Model.create(update)
						updated.push(instance)
					}
				}
				catch (error) {
					appNotify(`[${name}]syncUpdate.upsert`, { error, personId, update }, 'error')
					exclude.push(id)
				}
			}
			else {
				// both server & app changes
				const current = id
					? await Model.findById(id).catch(() => undefined)
					: await Model.findOne({ where: update }).catch(() => undefined)

				if (current) {
					const { server, app } = updatesFromChanges(current, serverChanges, update)
					// createdAt & modifiedAt should be set automatically by Timestamp mixin
					delete server.createdAt
					delete server.modifiedAt
					await current.updateAttributes(server)
					updated.push(app)
				}
			}

			if (Model.syncUpdate) {	// model specific second-stage updates
				Model.syncUpdate(update, personId, deviceId)
					.catch(err => appNotify(`[${Model.name}]syncUpdate`, { err, personId, update }, 'error'))
			}
		}

		return { updated, exclude }
	}

	/**
	 * Fetch objects for model to sync down to app
	 * @param	{String} personId
	 * @param	{Number|void} [syncUntil]
	 * @param	{Number|void} [limit] - not supported (for now)
	 * @param	{Object} install
	 * @param	{Object} updates
	 *				{String[]} exclude - omit from fetch (app already updated)
	 *				{Object[]} updated - changes for app
	 * @param	{ServerResponse} res
	 * @return	{Promise<Object[]>} objects to sync down to app
	 */
	Cache.syncFetch = async function(personId, syncUntil, limit, install, updates, res) {
		const { sync } = appModule(SYNC),
			{ app } = Cache,
			{ Action, Card, Offer, Reward, Message, Person, Place, AccountSetting, AppEvent } = app.models,
			models = [ Action, Card, Offer, Reward, Message, Person, Place, AccountSetting, AppEvent ],
			legacy = isPre7app(install),
			last = syncUntil ? new Date(syncUntil) : undefined,
			head = legacy ? '{"objects":[' : undefined,
			stream = sync.streams.create(res, install),
			fetches = []

		stream.begin(head)

		for (const model of models) {
			const { name } = model,
				mSync = appModule(SYNC).get(name)

			if (!mSync) {
				console.error(`[syncFetch] invalid model: '${name}'`)
				continue
			}

			const { settings } = mSync,
				{ fetch } = settings

			if (fetch === false) continue

			fetches.push(
				model.syncFetch(personId, last)
					.then(instances => {
						const syncUntil = syncUntilOf(instances),
							applied = Cache.applyUpdates(instances, updates),
							objects = mSync.packInstances(applied, legacy)

						stream.objects(objects)
						return { syncUntil, count: objects.length }
					})
			)
		}

		const results = await Promise.all(fetches),
			{ syncUntil: until, count } = results.reduce((t, r) => {
				const count = t.count + r.count,
					syncUntil = r.syncUntil > t.syncUntil ? r.syncUntil : t.syncUntil

				return { syncUntil, count }
			}, { syncUntil: Date.now(), count: 0 }),
			partial = false,
			tail = legacy
				? `],"syncUntil":${until},"partial":${partial}}`
				: { objects: count, syncUntil: until, partial }

		stream.end(tail)
	}

	/**
	 * Apply updates to fetched instances
	 * @param	{Object[]} instances
	 * @param	{Object} updates
	 *				{String[]} exclude - omit from fetch (app already updated)
	 *				{Object[]} updated - changes for app
	 * @return	{Object[]}
	 */
	Cache.applyUpdates = function(instances, updates) {
		const { exclude, updated } = updates,
			excluded = instances.reduce((res, i) => {
				if (!exclude.includes(String(i.id))) {
					res.push(i)
				}
				return res
			}, [])

		return excluded
	}
}
