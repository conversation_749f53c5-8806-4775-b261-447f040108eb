{"name": "Business", "plural": "Businesses", "description": "Perkd", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "relations": {}, "validations": [], "acls": [], "indexes": {}, "scopes": {}, "methods": {"doUpsert": {"description": "Create or update a business", "http": {"path": "/doUpsert", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true}, {"arg": "filter", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "getStores": {"description": "Get all the operating stores", "http": {"path": "/getStores", "verb": "get"}, "accepts": [{"arg": "from", "type": "string"}, {"arg": "to", "type": "string"}], "returns": {"type": "array", "root": true}}, "getStaff": {"description": "get Staff of given Role(s) during the (optional) Period", "http": {"path": "/getStaff", "verb": "get"}, "accepts": [{"arg": "roles", "type": "array"}, {"arg": "from", "type": "string"}, {"arg": "to", "type": "string"}], "returns": {"type": "array", "root": true}}, "getAssignment": {"description": "get Assignements (to stores) of given Staff(s) during the (optional) Period", "http": {"path": "/getAssignment", "verb": "get"}, "accepts": [{"arg": "staffList", "type": "array", "required": true}, {"arg": "from", "type": "string"}, {"arg": "to", "type": "string"}], "returns": {"type": "array", "root": true}}, "prototype.upsertSettings": {"description": "Create or update Settings", "http": {"path": "/upsertSettings", "verb": "post"}, "accepts": [{"arg": "settingList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.upsertDates": {"description": "Create or update Dates", "http": {"path": "/upsertDates", "verb": "post"}, "accepts": [{"arg": "dateList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.deleteDate": {"description": "Delete a date", "http": {"path": "/dates/:fk", "verb": "DELETE"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upsertLocales": {"description": "Create or update Locale", "http": {"path": "/upsertLocales", "verb": "post"}, "accepts": [{"arg": "locale", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.defaultLanguage": {"description": "Get / set default language", "http": {"path": "/languages/:fk/default", "verb": "post"}, "accepts": [{"arg": "language", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "string", "root": true}}, "prototype.upsertPhones": {"description": "Create or update Phones", "http": {"path": "/upsertPhones", "verb": "post"}, "accepts": [{"arg": "phoneList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.deletePhone": {"description": "Delete a related item by fullNumber for phone", "http": {"path": "/phones/:fk", "verb": "DELETE"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.defaultPhone": {"description": "Set default phone", "http": {"path": "/phones/:fk/default", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.replacePhone": {"description": "Replace phone number of a model instance", "http": {"path": "/phones/:fk/replace", "verb": "post"}, "accepts": [{"arg": "oldNumber", "type": "string", "required": true}, {"arg": "newNumber", "type": "any", "description": "New fullNumber or phone object", "required": true}, {"arg": "options", "type": "object", "description": "doUpsert options"}], "returns": {"type": "object", "root": true}}, "prototype.upsertEmails": {"description": "Create or update Emails", "http": {"path": "/upsertEmails", "verb": "post"}, "accepts": [{"arg": "emailList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.defaultEmail": {"description": "Set default email by address", "http": {"path": "/emails/:fk/default", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.replaceEmail": {"description": "Replace email of a model instance", "http": {"path": "/emails/:fk/replace", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.deleteEmail": {"description": "Delete a related item by address for email", "http": {"path": "/emails/:fk", "verb": "DELETE"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upsertLinks": {"description": "Create or update Links", "http": {"path": "/upsertLinks", "verb": "post"}, "accepts": [{"arg": "linkList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.deleteLink": {"description": "Delete a related item by id for link", "http": {"path": "/links/:fk", "verb": "DELETE"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upsertAddresses": {"description": "Create or update Addresses", "http": {"path": "/upsertAddresses", "verb": "post"}, "accepts": [{"arg": "addressList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.deleteAddress": {"description": "Delete a related item by id for address", "http": {"path": "/addresses/:fk", "verb": "DELETE"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.defaultAddress": {"description": "Set default address by id", "http": {"path": "/addresses/:fk/default", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.replaceAddress": {"description": "Replace address of a model instance", "http": {"path": "/addresses/:fk/replace", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upsertIdentities": {"description": "Create or update Identities", "http": {"path": "/upsertIdentities", "verb": "post"}, "accepts": [{"arg": "identityList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.replaceIdentity": {"description": "Replace an identity of the model", "http": {"path": "/identities/:fk/replace", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "newIdentity", "type": "any", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.deleteIdentity": {"description": "Delete a related item by id for identity", "http": {"path": "/identities/:fk", "verb": "DELETE"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upsertLocations": {"description": "Create or update Locations", "http": {"path": "/upsertLocations", "verb": "post"}, "accepts": [{"arg": "locationList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.deleteLocation": {"description": "Delete a related item by name for location", "http": {"path": "/locations/:fk", "verb": "DELETE"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}