{"name": "Form", "plural": "Forms", "base": "Model", "idInjection": true, "strict": false, "properties": {"id": false, "name": {"type": "String", "max": 16, "required": true}, "schema": {"type": "Object", "description": "List of field schema, ref: http://schemaform.io/examples/bootstrap-example.html (Kitchen Sink Example)"}, "view": {"type": "Object", "description": "List of field schema, ref: http://schemaform.io/examples/bootstrap-example.html (Kitchen Sink Example)"}, "mapping": {"type": "Object", "description": "Mapping data to filed value"}}, "validations": [], "acls": [], "scopes": {}, "methods": {}}