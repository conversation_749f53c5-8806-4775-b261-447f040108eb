{"name": "CardImage", "plural": "CardImages", "base": "Image", "idInjection": true, "strict": false, "options": {}, "mixins": {}, "properties": {"irregularShape": {"type": "boolean", "default": false}}, "validations": [], "acls": [], "scopes": {}, "methods": {"prototype.destroy": {"http": {"verb": "delete", "path": "/"}, "accepts": [{"arg": "fk", "type": "any", "description": "Foreign key for CardImage", "required": true, "http": {"source": "path"}}], "description": "Delete a related item by id for CardImage.", "returns": []}}}