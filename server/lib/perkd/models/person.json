{"name": "Person", "plural": "Persons", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true, "Errors": true}, "options": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"doFind": {"description": "Find all instances of the model matched by filter from the data source.", "http": {"path": "/find", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "array", "root": true}}, "doFindOne": {"description": "Find first instance of the model matched by filter from the data source.", "http": {"path": "/findOne", "verb": "post"}, "accepts": {"arg": "filter", "type": "object"}, "returns": {"type": "object", "root": true}}, "doCount": {"description": "Count instances of the model matched by where from the data source.", "http": {"path": "/count", "verb": "post"}, "accepts": {"arg": "where", "type": "object"}, "returns": {"type": "number", "root": true}}, "match": {"description": "Match existing instances by given profile & options", "http": {"path": "/match", "verb": "get"}, "accepts": [{"arg": "body", "type": "object", "http": {"source": "body"}, "required": true}], "returns": {"type": "array", "root": true}}, "search": {"description": "Search Persons by given string & options", "http": {"path": "/search", "verb": "get"}, "accepts": [{"arg": "searchString", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "doUpsert": {"description": "Create or update a person", "http": {"path": "/doUpsert", "verb": "post"}, "accepts": [{"arg": "data", "type": "object", "required": true}, {"arg": "filter", "type": "object"}, {"arg": "through", "type": "object"}], "returns": {"type": "Person", "root": true}}, "findByPerkdMobile": {"description": "Get person with perkd mobile (fullNumber)", "http": {"path": "/findByPerkdMobile", "verb": "get"}, "accepts": [{"arg": "mobile", "type": "string", "required": true}, {"arg": "options", "type": "object", "description": "noAccount = true => only if without account"}], "returns": {"type": "object", "root": true}}, "findOrCreateByMobile": {"description": "Find or create person with (registered) mobile", "http": {"path": "/findOrCreateByMobile", "verb": "post"}, "accepts": [{"arg": "mobile", "type": "string", "required": true}, {"arg": "data", "type": "object"}, {"arg": "through", "type": "object"}], "returns": {"type": "object", "root": true}}, "findByIdentity": {"description": "Get Person with Identity", "http": {"path": "/findByIdentity", "verb": "get"}, "accepts": [{"arg": "provider", "type": "string", "required": true}, {"arg": "type", "type": "string"}, {"arg": "externalId", "type": "string", "required": true}], "returns": {"type": "array", "root": true}}, "findIdentity": {"description": "Find an Identity for provider", "http": {"path": "/identity", "verb": "get"}, "accepts": [{"arg": "id", "type": "string", "required": true}, {"arg": "provider", "type": "string", "required": true}, {"arg": "type", "type": "string"}], "returns": {"type": "string", "root": true}}, "upsertIdentity": {"description": "Update or add identity", "http": {"path": "/identity/upsert", "verb": "post"}, "accepts": [{"arg": "id", "type": "string", "required": true, "description": "person id"}, {"arg": "provider", "type": "string", "required": true}, {"arg": "type", "type": "string"}, {"arg": "externalId", "type": "string", "required": true}]}, "syncFetch": {"description": "Used by Sync to fetch instances", "http": {"path": "/sync/fetch", "verb": "get"}, "accepts": [{"arg": "personId", "type": "string", "required": true}, {"arg": "last", "type": "date"}], "returns": {"type": ["Person"], "root": true}}, "prototype.upsertIdentities": {"description": "Create or update Identities", "http": {"path": "/upsertIdentities", "verb": "post"}, "accepts": [{"arg": "identityList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.replaceIdentity": {"description": "Replace an identity of the model", "http": {"path": "/identities/:fk/replace", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "newIdentity", "type": "any", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.deleteIdentity": {"description": "Delete identity", "http": {"path": "/deleteIdentity", "verb": "POST"}, "accepts": [{"arg": "identity", "type": "object", "required": true}, {"arg": "through", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upsertPhones": {"description": "Create or update Phones", "http": {"path": "/upsertPhones", "verb": "post"}, "accepts": [{"arg": "phoneList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.deletePhone": {"description": "Delete phone", "http": {"path": "/deletePhone", "verb": "POST"}, "accepts": [{"arg": "phone", "type": "object", "required": true}, {"arg": "through", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.defaultPhone": {"description": "Set default phone", "http": {"path": "/phones/:fk/default", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.replacePhone": {"description": "Replace phone number of a model instance", "http": {"path": "/phones/replace", "verb": "post"}, "accepts": [{"arg": "oldNumber", "type": "string", "required": true}, {"arg": "newNumber", "type": "any", "description": "New fullNumber or phone object", "required": true}, {"arg": "options", "type": "object", "description": "doUpsert options"}], "returns": {"type": "object", "root": true}}, "prototype.upsertEmails": {"description": "Create or update Emails", "http": {"path": "/upsertEmails", "verb": "post"}, "accepts": [{"arg": "emailList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.defaultEmail": {"description": "Set default email by address", "http": {"path": "/emails/:fk/default", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.replaceEmail": {"description": "Replace email of a model instance", "http": {"path": "/emails/:fk/replace", "verb": "post"}, "accepts": [{"arg": "fk", "type": "string", "required": true}, {"arg": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.deleteEmail": {"description": "Delete email", "http": {"path": "/deleteEmail", "verb": "POST"}, "accepts": [{"arg": "email", "type": "object", "required": true}, {"arg": "through", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.upsertBehaviors": {"description": "Create or update Behaviors", "http": {"path": "/upsertBehaviors", "verb": "post"}, "accepts": [{"arg": "subModel", "type": "string", "required": true}, {"arg": "behaviorList", "type": "array", "required": true}], "returns": {"type": "array", "root": true}}, "prototype.deleteBehavior": {"description": "Delete a behavior", "http": {"path": "/deleteBehavior", "verb": "post"}, "accepts": [{"arg": "subModel", "type": "string", "required": true}, {"arg": "behavior", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.deleteDate": {"description": "Delete date", "http": {"path": "/deleteDate", "verb": "POST"}, "accepts": [{"arg": "date", "type": "object", "required": true}, {"arg": "through", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.deleteAddress": {"description": "Delete address", "http": {"path": "/deleteAddress", "verb": "POST"}, "accepts": [{"arg": "address", "type": "object", "required": true}, {"arg": "through", "type": "object"}], "returns": {"type": "object", "root": true}}, "prototype.getActivities": {"description": "Get activities with provided options of the person", "http": {"path": "/getActivities", "verb": "get"}, "accepts": [{"arg": "options", "type": "object", "http": {"source": "body"}}], "returns": {"type": "array", "root": true}}, "prototype.upsertPermissions": {"description": "Create or update Permissions", "http": {"path": "/upsertPermissions", "verb": "post"}, "accepts": [{"arg": "permissionList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "prototype.addTag": {"description": "Add one or more tags to model instance", "http": {"path": "/tag/add", "verb": "post"}, "accepts": [{"arg": "tag", "type": "array", "required": true}, {"arg": "kind", "type": "string", "default": "user"}], "returns": {"type": "array", "root": true}}, "prototype.removeTag": {"description": "Remove one or more tags from model instance", "http": {"path": "/tag/remove", "verb": "post"}, "accepts": [{"arg": "tag", "type": "array", "required": true}, {"arg": "kind", "type": "string", "default": "user"}], "returns": {"type": "array", "root": true}}}}