{"name": "LogoImage", "plural": "LogoImages", "base": "Image", "idInjection": true, "strict": false, "options": {}, "mixins": {}, "properties": {"name": {"type": "String", "max": 16, "description": "unique identifier, eg. dark, light"}, "bgcolor": {"type": "String"}}, "validations": [], "relations": {}, "acls": [], "indexes": {"name_index": {"keys": {"name": 1}}}, "scopes": {}, "methods": {"prototype.destroy": {"http": {"verb": "delete", "path": "/"}, "accepts": [{"arg": "fk", "type": "any", "description": "Foreign key for LogoImage", "required": true, "http": {"source": "path"}}], "description": "Delete a related item by id for LogoImage.", "returns": []}}}