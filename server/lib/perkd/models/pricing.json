{"name": "Pricing", "description": "", "idInjection": false, "strict": false, "options": {"validateUpsert": true}, "properties": {"id": false, "name": {"type": "String", "max": 16}, "title": {"type": "string", "max": 64, "description": "Linked (copied from) 'title' from (product) Variant in CRM, used for Credit Card statement"}, "currency": {"type": "String", "max": 3, "description": "ISO 4217"}, "unitPrice": {"type": "number", "description": "MUST be in zero-decimals format"}, "salePrice": {"type": {"value": {"type": "number"}, "startAt": {"type": "date"}, "endAt": {"type": "date"}}}, "variantId": {"type": "string", "description": "Product Variant Id set by CRM, MUST be passed back to CRM when generating Order"}, "limits": {"type": {"perUser": {"type": "number", "default": 0, "description": "Number of cards allowed per user. 0 => no limit"}, "perOrder": {"type": "number", "default": 0, "description": "Quantity allowed per Order. 0 => no limit"}}}, "tenure": {"type": "string", "required": true, "description": "Duration of membership granted upon purchase. ISO8601 format, eg. P365D"}, "storedValue": {"type": {"balance": {"type": "number", "default": 0, "description": "Balance stored value (integer)"}, "currency": {"type": {"code": {"type": "string", "max": 8, "description": "ISO 4217 currency code, 'CREDIT' or 'POINT'"}, "precision": {"type": "number", "default": 0, "description": "Decimal precision, ie. 2 => amount of 1000 = $10.00"}}, "required": true}}, "description": "Initial stored value (used by 'storedValue' fulfillmentService)"}, "fee": {"type": {"card": {"percent": {"type": "number", "description": "Perkd fees in decimal percentage, eg. 0.1 => 10%"}, "amount": {"type": "number", "description": "Perkd fees in native precision, ie. 10 cents = 0.10"}}, "alipay": {"percent": {"type": "number"}, "amount": {"type": "number"}}, "wechatpay": {"percent": {"type": "number"}, "amount": {"type": "number"}}}}, "paymentMethods": [{"type": "string", "description": "if [] = fallback to methods in Payments", "enum": ["applepay", "googlepay", "alipay", "wechatpay"]}]}, "scopes": {}, "methods": {}}