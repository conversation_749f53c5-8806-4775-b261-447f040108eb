{"name": "<PERSON><PERSON>", "plural": "Rewards", "description": "Perkd", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {"validateUpsert": true}, "mixins": {"MultitenantRemote": true, "Errors": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "validations": [], "relations": {}, "acls": [], "scopes": {}, "methods": {"issue": {"description": "Issue rewards to collection of cards", "http": {"path": "/issue", "verb": "post"}, "accepts": [{"arg": "targets", "type": "object", "required": true, "description": "Collection of { cardId: [reward], cardId: [] }"}, {"arg": "notification", "type": "object", "description": "Notification params, null => no notification"}], "returns": {"type": "object", "root": true}}, "syncFetch": {"description": "Used by Sync to fetch instances", "http": {"path": "/sync/fetch", "verb": "get"}, "accepts": [{"arg": "personId", "type": "string", "required": true}, {"arg": "last", "type": "date"}], "returns": {"type": ["<PERSON><PERSON>"], "root": true}}, "prototype.issueStamps": {"description": "Issue stamps for reward", "http": {"path": "/stamps/issue", "verb": "post"}, "accepts": [{"arg": "reward", "type": "object", "required": true, "description": "reward instance"}, {"arg": "quantity", "type": "number", "required": true, "description": "Number of stamps issued"}, {"arg": "notification", "type": "object", "description": "Notification params, null => no notification"}], "returns": {"type": "<PERSON><PERSON>", "root": true}}, "prototype.deductStamps": {"description": "Deduct stamps for reward", "http": {"path": "/stamps/deduct", "verb": "post"}, "accepts": [{"arg": "reward", "type": "object", "required": true, "description": "reward instance"}, {"arg": "quantity", "type": "number", "required": true, "description": "Number of stamps deducted"}, {"arg": "notification", "type": "object", "description": "Notification params, null => no notification"}], "returns": {"type": "<PERSON><PERSON>", "root": true}}, "prototype.cancel": {"description": "Cancel reward", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}, {"arg": "reason", "type": "string"}], "returns": {"type": "<PERSON><PERSON>", "root": true}}, "prototype.recover": {"description": "Recover cancelled reward", "http": {"path": "/recover", "verb": "post"}, "accepts": [], "returns": {"type": "<PERSON><PERSON>", "root": true}}, "prototype.extend": {"description": "Extend reward validity (endTime)", "http": {"path": "/extend", "verb": "post"}, "accepts": [{"arg": "endTime", "type": "date", "required": true}, {"arg": "notification", "type": "object", "description": "Notification params, null => no notification"}], "returns": {"type": "<PERSON><PERSON>", "root": true}}}}