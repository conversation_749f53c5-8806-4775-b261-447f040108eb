{"name": "Account", "plural": "Accounts", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {"settings": {"type": "hasOne", "model": "AccountSetting", "foreignKey": "accountId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"createMessage": {"description": "Send a verification code message (only used by verify.js campaign)", "http": {"path": "/createMessage", "verb": "post"}, "accepts": [{"arg": "accountId", "type": "any", "required": true}, {"arg": "purpose", "type": "string", "required": true}, {"arg": "channel", "type": "string", "required": true}, {"arg": "code", "type": "string", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}