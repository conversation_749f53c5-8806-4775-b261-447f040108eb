{"name": "Payment", "plural": "Payments", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"prototype.createCustomer": {"description": "Create a Customer at provider for storing PaymentMethod later", "http": {"path": "/customers", "verb": "post"}, "accepts": [{"arg": "personId", "type": "any", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.addPaymentMethod": {"description": "Create a PaymentMethod & SetupIntent for a Customer", "http": {"path": "/payment/method", "verb": "post"}, "accepts": [{"arg": "method", "type": "object", "required": true}, {"arg": "customerId", "type": "string", "required": true}, {"arg": "options", "type": "object", "description": "{ metadata, description }"}], "returns": {"type": "object", "root": true}}, "prototype.removePaymentMethod": {"description": "Detach a PaymentMethod from a Customer", "http": {"path": "/payment/method", "verb": "delete"}, "accepts": [{"arg": "method", "type": "object", "description": "{ external: { paymentMethodId } }", "required": true}], "returns": {"type": "object", "root": true}}}}