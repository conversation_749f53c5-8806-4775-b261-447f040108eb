{"name": "PlaceList", "plural": "PlaceLists", "base": "PersistedModel", "options": {}, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "relations": {}, "acls": [], "scopes": {}, "methods": {"getDefaultList": {"description": "Get default PlaceList for tenant", "http": {"path": "/default", "verb": "get"}, "accepts": [{"arg": "tenant", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "fetch": {"description": "Fetch place list (App API)", "http": {"path": "/app/fetch", "verb": "post"}, "accepts": [{"arg": "objectIds", "type": "array", "required": true}, {"arg": "options", "type": "object", "required": false}], "returns": {"type": "object", "root": true}}}}