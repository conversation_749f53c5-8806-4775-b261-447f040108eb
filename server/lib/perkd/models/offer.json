{"name": "Offer", "plural": "Offers", "description": "Perkd", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {}, "mixins": {"MultitenantRemote": true, "Errors": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "acls": [], "scopes": {}, "methods": {"issue": {"description": "Issue offers to collection of cards", "http": {"path": "/issue", "verb": "post"}, "accepts": [{"arg": "targets", "type": "object", "required": true, "description": "Collection of { cardId: [offer], cardId: [] }"}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "syncFetch": {"description": "Used by Sync to fetch instances", "http": {"path": "/sync/fetch", "verb": "get"}, "accepts": [{"arg": "personId", "type": "string", "required": true}, {"arg": "last", "type": "date"}], "returns": {"type": ["Offer"], "root": true}}, "prototype.redeem": {"description": "Redeem offer", "http": {"path": "/redeem", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}, {"arg": "quantity", "type": "number"}, {"arg": "through", "type": "object"}, {"arg": "items", "type": "any"}], "returns": {"type": "Offer", "root": true}}, "prototype.revert": {"description": "Reverse redeemed offer", "http": {"path": "/revert", "verb": "post"}, "accepts": [{"arg": "quantity", "type": "number"}], "returns": {"type": "Offer", "root": true}}, "prototype.cancel": {"description": "Cancel offer", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}, {"arg": "reason", "type": "string"}], "returns": {"type": "Offer", "root": true}}, "prototype.recover": {"description": "Recover cancelled offer", "http": {"path": "/recover", "verb": "post"}, "accepts": [{"arg": "at", "type": "date"}, {"arg": "reason", "type": "string"}], "returns": {"type": "Offer", "root": true}}, "prototype.extend": {"description": "Extend offer validity (endTime)", "http": {"path": "/extend", "verb": "post"}, "accepts": [{"arg": "endTime", "type": "date", "required": true}, {"arg": "notification", "type": "object", "description": "Notification params, null => no notification"}], "returns": {"type": "Offer", "root": true}}, "prototype.recall": {"description": "Recall offer", "http": {"path": "/recall", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "Offer", "root": true}}}}