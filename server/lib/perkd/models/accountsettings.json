{"name": "AccountSetting", "plural": "AccountSettings", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {"validateUpsert": true}, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "validations": [], "relations": {}, "acls": [], "scopes": {}, "methods": {"syncFetch": {"description": "Used by Sync to fetch instances", "http": {"path": "/sync/fetch", "verb": "get"}, "accepts": [{"arg": "personId", "type": "string", "required": true}, {"arg": "last", "type": "date"}], "returns": {"type": ["AccountSetting"], "root": true}}}}