{"name": "Permission", "plural": "Permissions", "base": "Model", "idInjection": false, "strict": false, "mixins": {}, "options": {}, "properties": {"channel": {"type": "String", "description": "Type of permission", "id": true, "max": 32, "enum": ["serviceterms", "privacypolicy", "mobile", "email", "postal", "voice", "push"]}, "feature": {"type": "String", "description": "Permission requestable for devices", "max": 32, "enum": ["location", "camera", "photos", "contacts", "calendar", "homekit", "health", "speechrecognition", "blecentral", "bleperipheral", "microphone", "motionfitness", "notifications", "backgroundfetch"]}, "status": {"type": "string", "description": ["unknown - before request & requestable", "blocked - denied by user, (on Android choose 'Never ask again')", "denied - denied by user, ask again (Android only)", "allowed - Permission granted"], "enum": ["unknown", "blocked", "denied", "allowed"], "default": "unknown"}, "options": {"type": [{"type": "string", "enum": ["donotdisturb", "always", "<PERSON><PERSON><PERSON>", "badge", "alert", "sound"]}], "description": "Finer control of permission.", "default": []}, "grantedAt": {"type": "Date", "default": null}, "revokedAt": {"type": "Date", "default": null}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {}}