{"name": "CardMaster", "plural": "CardMasters", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true, "Errors": true, "RemotingTypes": {"Card": true}, "ListOwnerRemote": {"ListModel": "PlaceList"}}, "options": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "relations": {"cardImages": {"type": "hasMany", "model": "CardImage", "foreignKey": "ownerId"}, "logos": {"type": "hasMany", "model": "LogoImage", "foreignKey": "ownerId"}, "issuer": {"type": "belongsTo", "model": "Business", "foreignKey": "issuerId"}, "cards": {"type": "hasMany", "model": "Card", "foreignKey": "masterId"}, "placeLists": {"type": "referencesMany", "model": "PlaceList", "foreignKey": "placeListIds"}}, "acls": [], "scopes": {}, "methods": {"findOneAndUpdate": {"description": "Find first instance of the model matched by filter and update it in one atomic operation.", "http": {"path": "/findOneAndUpdate", "verb": "post"}, "accepts": [{"arg": "filter", "type": "object"}, {"arg": "data", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "CardMaster", "root": true}}, "getBarcodeTypes": {"description": "Get all barcode types the card master supports", "http": {"path": "/getBarcodeTypes", "verb": "get"}, "returns": {"type": "object", "root": true}}, "getCategories": {"description": "Get card master supported categories", "http": {"path": "/getCategories", "verb": "get"}, "returns": {"type": "object", "root": true}}, "canIssueCard": {"description": "Check issue card", "http": {"path": "/canIssueCard", "verb": "post"}, "accepts": [{"arg": "masterId", "type": "any"}, {"arg": "personId", "type": "any"}, {"arg": "numberOfCards", "type": "number"}], "returns": {"type": "object", "root": true}}, "matchCard": {"description": "Find Card Master for Card.", "http": {"path": "/match/card", "verb": "post"}, "accepts": [{"arg": "card", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "issue": {"description": "Create and issue card", "http": {"path": "/issue", "verb": "post"}, "accepts": [{"arg": "masterId", "type": "any"}, {"arg": "personId", "type": "any"}, {"arg": "card", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "findOrIssueCard": {"description": "Find or issue new card", "http": {"path": "/findOrIssueCard", "verb": "post"}, "accepts": [{"arg": "personId", "type": "any"}, {"arg": "masterIds", "type": "array", "description": "Find card instance in order of array"}, {"arg": "masterId", "type": "any", "description": "Specify masterId for issue new card if not found"}, {"arg": "options", "type": "object", "description": "{ through }"}], "returns": {"type": "array", "root": true, "description": "[card, isNew]"}}, "cardTransferred": {"description": "Called when recipient accepted card transfer", "http": {"path": "/card/transferred", "verb": "post"}, "accepts": [{"arg": "masterId", "type": "any", "required": true}, {"arg": "fromCardId", "type": "any", "required": true}, {"arg": "toCardId", "type": "any", "required": true}, {"arg": "toUserId", "type": "any", "required": true}, {"arg": "mode", "type": "string", "required": true, "description": "transfer OR send"}], "returns": {"type": "object", "root": true}}, "getPlaceListIds": {"description": "Find placeListIds of cardMasterIds", "http": {"path": "/placeListIds/ids", "verb": "post"}, "accepts": [{"arg": "ids", "type": "array", "required": true}], "returns": {"type": ["object"], "root": true}}, "findCachedById": {"description": "Find CardMasterPub instance by id from cached data", "http": {"path": "/cached/cardMasterPub", "verb": "post"}, "accepts": [{"arg": "id", "type": "any", "required": true}, {"arg": "fields", "type": "array"}], "returns": {"type": "object", "root": true}}, "prototype.canIssueCard": {"description": "Check issue card", "http": {"path": "/canIssueCard", "verb": "post"}, "accepts": [{"arg": "personId", "type": "any"}, {"arg": "numberOfCards", "type": "number"}], "returns": {"type": "object", "root": true}}, "prototype.issue": {"description": "Create and issue card", "http": {"path": "/issue", "verb": "post"}, "accepts": [{"arg": "personId", "type": "any"}, {"arg": "card", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "prototype.register": {"description": "Register card (used by Card instance)", "http": {"path": "/register/card", "verb": "post"}, "accepts": [{"arg": "personId", "type": "any", "required": true}, {"arg": "formData", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "prototype.clone": {"description": "<PERSON>lone Card Master", "http": {"path": "/clone", "verb": "post"}, "returns": {"type": "CardMaster", "root": true}}, "prototype.createShared": {"description": "Create a shared instance (called by Share mixin)", "http": {"path": "/shared/card", "verb": "post"}, "accepts": [{"arg": "mode", "type": "string", "required": true, "enum": ["invite", "clone", "transfer"]}, {"arg": "personId", "type": "any", "required": true, "description": "Of sharer"}, {"arg": "cardId", "type": "any", "description": "Of receiving card (only for non-card sharing)"}, {"arg": "source", "type": "object", "required": true, "description": "Originating card"}, {"arg": "sharer", "type": "object", "required": true, "description": "Sharer object"}, {"arg": "recipient", "type": "object", "required": true, "description": "Recipient object"}, {"arg": "options", "type": "object", "description": "{ through }"}], "returns": {"type": "Card", "root": true}}, "prototype.validateForm": {"description": "Validate form data", "http": {"path": "/form/validate", "verb": "post"}, "accepts": [{"arg": "name", "type": "string", "required": true}, {"arg": "data", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.publish": {"description": "Publish Card Master", "http": {"path": "/publish", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}