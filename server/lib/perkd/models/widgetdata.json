{"name": "WidgetData", "base": "PersistedModel", "idInjection": true, "strict": true, "options": {}, "mixins": {"MultitenantRemote": true, "Errors": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "personId": {"type": "string"}, "cardId": {"type": "string"}, "cardMasterId": {"type": "string"}, "key": {"type": "string", "max": 32, "description": "Unique key of widget"}, "data": {"type": "object", "description": "For storing custom data of each user", "default": {}}, "badge": {"type": {"unread": {"type": "number", "default": null, "description": "Synced Widget unread badge count, null => none, 0 => dot (ie. bag item count)"}, "valid": {"type": "number", "default": null, "description": "Synced Widget valid badge count, null => none (ie. ordertrack track count)"}}, "default": {}}, "value": {"type": "string", "description": "Summary value of data (ie. points balance shown as widget icon)"}, "merge": {"type": "string", "enum": ["shallow", "deep"], "default": "shallow", "description": "Merge strategy for 'data' property when sync up WidgetData"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "validations": [], "relations": {"widget": {"type": "belongsTo", "model": "Widget", "foreignKey": "widgetId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"syncFetch": {"description": "Used by Sync to fetch instances", "http": {"path": "/sync/fetch", "verb": "get"}, "accepts": [{"arg": "personId", "type": "string", "required": true}, {"arg": "last", "type": "date"}], "returns": {"type": ["WidgetData"], "root": true}}}}