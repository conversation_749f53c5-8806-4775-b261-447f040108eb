{"name": "Widget", "plural": "Widgets", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {}, "mixins": {"MultitenantRemote": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "name": {"type": "string", "max": 32, "required": true, "description": "Name of widget"}, "key": {"type": "string", "max": 16, "required": true, "description": "Unique key of widget"}, "kind": {"type": "string", "required": true, "description": "Type of widget", "enum": ["uri", "applet", "place", "wifi", "share", "offer", "message", "reward", "ticket", "pay"]}, "startTime": {"type": "date"}, "endTime": {"type": "date"}, "label": {"type": "String"}, "icon": {"type": {"style": {"type": "string", "description": "Icon style", "enum": ["image", "value", "both", "icon"]}, "uri": {"type": "string", "description": "Resource path or remote url"}, "value": {"type": "string"}, "props": {"type": "object", "description": "icon properties, ref: https://react-native-training.github.io/react-native-elements/docs/0.19.1/icon.html"}}}, "order": {"type": "number", "description": "Priority, widget with negative number will be placed on tabbar"}, "steps": {"type": [{"type": "string", "enum": ["request", "share", "approval", "payment", "register", "done"]}], "description": "Display widget in card detail at specified steps only"}, "badge": {"number": {"type": "number"}, "style": {"type": "string", "default": "standard", "enum": ["standard"]}}, "minAppVersion": {"type": "String", "description": "Minimum supported app version, 2.1 (major.minor)"}, "applet": {"type": "any", "description": "Applet key"}, "param": {"type": "object", "description": "Parameters, may be overriden by Card Master"}, "sync": {"type": "boolean", "default": true, "description": "Supports sync up WidgetData"}, "merge": {"type": "string", "enum": ["shallow", "deep"], "default": "shallow", "description": "Merge strategy for 'data' property when sync up WidgetData"}, "platform": {"type": {"ios": {"type": "object"}, "android": {"type": "object"}}, "description": "Platform specific value for widget properties"}, "roaming": {"type": "object", "description": "Country specific value for widget properties"}, "requires": {"type": {"cardMasterIds": {"type": ["string"]}, "permissions": {"type": ["string"], "description": "Permission keys in installation"}, "network": {"type": "boolean", "description": "Must be online"}, "checkIn": {"type": "boolean", "description": "Must be checked-in"}}, "description": "Conditions to be met to access widget"}, "globalize": {"type": "Globalize"}, "scriptFile": {"type": "string", "description": "Name of handler script file"}, "initData": {"type": "object", "description": "Initial data for widget"}, "enabled": {"type": "boolean", "description": "if disabled, widget will not be published"}, "visible": {"type": "boolean", "description": "When visible, display widget in card details"}, "qualifiers": {"type": "object", "description": "Qualifiers to control visibility"}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "validations": [], "relations": {"data": {"type": "hasMany", "model": "WidgetData", "foreignKey": "widgetId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"prototype.start": {"description": "Start widget data collection", "http": {"path": "/start", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.restart": {"description": "Restart when handler script updated", "http": {"path": "/restart", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}, "prototype.stop": {"description": "Stop widget data collection", "http": {"path": "/stop", "verb": "post"}, "accepts": [], "returns": {"type": "object", "root": true}}}}