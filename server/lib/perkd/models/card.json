{"name": "Card", "plural": "Cards", "description": "Perkd", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true, "Errors": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "acls": [], "scopes": {}, "methods": {"match": {"description": "Match existing cards by given profile & options", "http": {"path": "/match", "verb": "post"}, "accepts": [{"arg": "body", "type": "object", "http": {"source": "body"}, "required": true}], "returns": {"type": "object", "root": true}}, "matchCardMaster": {"description": "Match all remaining cards to card masters", "http": {"path": "/matchCardMaster", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "CardMaster", "root": true}}, "matchByImage": {"description": "Match existing custom cards by uploaded image", "http": {"path": "/matchByImage", "verb": "post"}, "accepts": [{"arg": "req", "type": "object", "http": {"source": "req"}}, {"arg": "res", "type": "object", "http": {"source": "res"}}, {"arg": "options", "type": "object"}], "returns": {"type": "array", "root": true}}, "getMatching": {"description": "Get cards with matched card masters", "http": {"path": "/getMatching", "verb": "get"}, "accepts": [{"arg": "filter", "type": "object"}], "returns": {"type": "object", "root": true}}, "getRemaining": {"description": "Get custom cards that are not matched", "http": {"path": "/getRemaining", "verb": "get"}, "accepts": [{"arg": "filter", "type": "object", "required": true}], "returns": {"type": "object", "root": true}}, "changeMasterNotification": {"description": "Notify users of card master change", "http": {"path": "/changeMasterNotification", "verb": "post"}, "accepts": [{"arg": "personId", "type": "any", "required": true}, {"arg": "cardIdList", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}, "getPushContent": {"description": "Get standard card update push content", "http": {"path": "/getPushContent", "verb": "get"}, "accepts": [{"arg": "personId", "type": "any", "required": true}, {"arg": "cardIdList", "type": "array", "required": true}], "returns": {"type": "string", "root": true}}, "findPersonIds": {"description": "Get personId for list of cardIds (Message API)", "http": {"path": "/findPersonIds", "verb": "post"}, "accepts": [{"arg": "cardIds", "type": "array", "required": true}], "returns": {"type": "array", "root": true}}, "activeCardsOf": {"description": "Active cards of person, optionally for cardmasters", "http": {"path": "/active/person", "verb": "get"}, "accepts": [{"arg": "personId", "type": "any", "required": true}, {"arg": "masterIds", "type": ["any"], "description": "Restrict to these cardmasters"}], "returns": {"type": ["Card"], "root": true}}, "getMasterIdsByPersonId": {"description": "CardMastarIds of person", "http": {"path": "/masterIds/person", "verb": "get"}, "accepts": [{"arg": "personId", "type": "string", "required": true}], "returns": {"type": ["string"], "root": true}}, "syncFetch": {"description": "Used by Sync to fetch instances", "http": {"path": "/sync/fetch", "verb": "get"}, "accepts": [{"arg": "personId", "type": "string", "required": true}, {"arg": "last", "type": "date"}], "returns": {"type": ["Card"], "root": true}}, "syncUpdate": {"description": "Used by Sync for second stage model-specific updates", "http": {"path": "/sync/update", "verb": "post"}, "accepts": [{"arg": "object", "type": "object", "required": true}, {"arg": "personId", "type": "string", "required": true}, {"arg": "deviceId", "type": "string", "required": true}], "returns": {"type": "object", "root": true}}, "prototype.issue": {"description": "Issue a card", "http": {"path": "/issue", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "prototype.cancel": {"description": "Cancel an issued card", "http": {"path": "/cancel", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "prototype.recover": {"description": "Recover a card to active state", "http": {"path": "/recover", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "prototype.revoke": {"description": "Revoke card", "http": {"path": "/revoke", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "prototype.edit": {"description": "Edit formData of Card.", "http": {"path": "/edit", "verb": "patch"}, "accepts": [{"arg": "formData", "type": "object", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "prototype.update": {"description": "Update card", "http": {"path": "/update", "verb": "post"}, "accepts": [{"arg": "data", "type": "object"}, {"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "prototype.terminate": {"description": "Terminate card immediately", "http": {"path": "/terminate", "verb": "post"}, "accepts": [{"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "prototype.share": {"description": "Share (app API)", "http": {"path": "/app/share", "verb": "post"}, "accepts": [{"arg": "recipient", "type": "object", "required": true, "description": "{ personId, mobile }"}, {"arg": "mode", "type": "string", "required": true, "enum": ["invite", "clone", "transfer"]}, {"arg": "options", "type": "object", "description": "{ channel }"}], "returns": {"type": "Card", "root": true}}, "prototype.changeMaster": {"description": "Change master of card", "http": {"path": "/master", "verb": "post"}, "accepts": [{"arg": "newMasterId", "type": "any"}, {"arg": "options", "type": "object"}], "returns": {"type": "Card", "root": true}}, "prototype.migrate": {"description": "Change owner (Person) of card", "http": {"path": "/migrate", "verb": "post"}, "accepts": [{"arg": "personId", "type": "any", "required": true, "description": "target person id"}, {"arg": "options", "type": "object", "description": "{ through }"}], "returns": {"type": "Card", "root": true}}, "prototype.softDelete": {"description": "<PERSON> as deleted.", "http": {"path": "/softDelete", "verb": "delete"}, "accepts": [{"arg": "through", "type": "object"}], "returns": {"type": "Card", "root": true}}}}