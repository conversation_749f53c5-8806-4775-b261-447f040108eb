/**
 *  @module Model:Permission
 */
const { values } = require('lodash'),
	{ isEmptyObj } = require('@perkd/utils')

const STATUS = {
		ALLOWED: 'allowed',
		DENIED: 'denied',
		UNKNOWN: 'unknown',
	},
	OPTIONS = {
		DONOTDISTURB: 'doNotDisturb',
	}

module.exports = function(Permission) {
	// -----  Static Properties  -----
	Permission.CHANNELS = {}		// initialized in Permission mixin

	// -----  Static Methods  -----

	Permission.default = function() {
		const permissionList = []
		Permission.CHANNELS = this.app.models.Person.CHANNELS
		for (const key in Permission.CHANNELS) {
			permissionList.push({ channel: Permission.CHANNELS[key], status: STATUS.UNKNOWN })
		}
		return permissionList
	}

	// -----  Validations  -----
	Permission.validate('channel', channelType, { message: 'Invalid channel type' })

	function channelType(err) {
		if (isEmptyObj(Permission.CHANNELS)) {
			Permission.CHANNELS = Permission.app.models.Person.CHANNELS
		}
		// Permission.CHANNELS should be initialized before validation is needed
		if (values(Permission.CHANNELS).indexOf(this.channel) < 0) err()
	}

	// -----  Operation hooks  -----

	Permission.observe('before save', (ctx, next) => {
		const updated = ctx.instance || ctx.data,
			existingStatus = ctx.currentInstance ? ctx.currentInstance.status : STATUS.UNKNOWN

		if (updated.status && updated.status !== existingStatus) {
			const
				existingGrant = ctx.currentInstance ? ctx.currentInstance.grantedAt : null,
				existingRevoke = ctx.currentInstance ? ctx.currentInstance.revokedAt : null,
				isDeny = status => status === STATUS.UNKNOWN || status === STATUS.DENIED,
				isAllow = status => status !== STATUS.UNKNOWN && status !== STATUS.DENIED

			if (isDeny(existingStatus) && isAllow(updated.status)) {
				updated.grantedAt = updated.grantedAt ? new Date(updated.grantedAt) : new Date()
				updated.revokedAt = existingRevoke || null
			}
			if (isAllow(existingStatus) && isDeny(updated.status)) {
				updated.revokedAt = updated.revokedAt ? new Date(updated.revokedAt) : new Date()
				updated.grantedAt = existingGrant || null
			}
		}
		next()
	})
}
