{"name": "Installation", "plural": "Installations", "base": "PersistedModel", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {}, "properties": {"id": {"type": "String", "description": "UUID of device", "id": true, "generated": true}, "app": {"type": {"id": {"type": "String"}, "version": {"type": "String", "description": "Perkd app version or Merchant app version. ie. '4.0.0'"}, "env": {"type": "String", "enum": ["dev", "prod", "train"]}}, "default": {}}, "badge": {"type": "Number", "default": 0, "description": "Current number on app badge"}, "device": {"type": {"uuid": {"type": "String"}, "name": {"type": "String"}, "brand": {"type": "String", "max": 32}, "manufacturer": {"type": "String", "max": 32}, "model": {"type": "String"}}, "default": {}}, "os": {"type": {"name": {"type": "String"}, "version": {"type": "String"}}, "default": {}}, "carrier": {"type": {"name": {"type": "String", "max": 16}, "country": {"type": "String", "description": "ISO 3166-1 Alpha-2", "length": 2}, "mobileCountryCode": {"type": "number"}}, "default": {}}, "locale": {"type": "Locale", "default": {}}, "geo": {"type": "Geometry", "description": "Geolocation of installation", "default": {}}, "capabilities": {"type": [{"type": {"name": {"type": "String", "max": "32", "enum": ["voice", "messaging", "payment", "bluetooth"]}, "support": [{"type": "string", "description": "Name of supported type, eg. 'applepay', 'alipay'", "max": 16}]}}], "default": []}, "permissions": {"type": ["Permission"], "default": []}, "tokens": {"type": {"apns": {"type": {"token": {"type": "string"}, "invalid": {"type": "boolean"}, "modifiedAt": {"type": "date"}}}, "fcm": {"type": {"token": {"type": "string"}, "invalid": {"type": "boolean"}, "modifiedAt": {"type": "date"}}}, "gcm": {"type": {"token": {"type": "string"}, "invalid": {"type": "boolean"}, "modifiedAt": {"type": "date"}}}, "jpush": {"type": {"token": {"type": "string"}, "invalid": {"type": "boolean"}, "modifiedAt": {"type": "date"}}}}, "description": "App tokens", "default": {}}, "appList": {"type": [{"type": "string"}], "description": "Names of (partner) apps installed", "default": []}, "loggedIn": {"type": "Boolean", "description": "App is in login state", "default": false}, "isBlocked": {"type": "boolean", "default": false}, "state": {"type": "string", "description": "Presence states - offline: quit app, suspend: app in bkgd, online: in-app", "default": "offline", "enum": ["online", "offline", "suspend", "uninstalled"]}, "stateSince": {"type": "Date", "default": null}, "lastSeenAt": {"type": "Date", "description": "Time of last app use", "default": null}, "createdAt": {"type": "Date", "default": null}, "modifiedAt": {"type": "Date", "default": null}, "deletedAt": {"type": "Date", "default": null}}, "validations": [], "relations": {"account": {"type": "belongsTo", "model": "Account", "foreignKey": "accountId"}, "person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}}, "acls": [], "scopes": {}, "methods": {"findByIds": {"description": "Find all instances from the data source.", "accessType": "READ", "http": {"path": "/findByIds", "verb": "get"}, "accepts": [{"arg": "ids", "type": "array", "required": true}, {"arg": "query", "type": "object"}], "returns": {"type": "array", "root": true}}, "refresh": {"description": "Refresh states, update references in Person when necessary", "http": {"path": "/refresh", "verb": "post"}, "accepts": [{"arg": "data", "type": "object"}], "returns": {"type": "Installation", "root": true}}, "findByPersonId": {"description": "Find (active) installations for person", "http": {"path": "/persons/:personId", "verb": "get"}, "accepts": [{"arg": "personId", "type": "any", "http": {"source": "path"}, "required": true}, {"arg": "options", "type": "object", "description": "{loggedIn:true}"}], "returns": {"type": "array", "root": true}}, "findByPersonIdBulk": {"description": "Find (active) installations for persons", "http": {"path": "/persons", "verb": "get"}, "accepts": [{"arg": "personIds", "type": "array", "required": true}, {"arg": "options", "type": "object", "description": "{loggedIn:true}"}], "returns": {"type": "object", "root": true}}, "findByCardId": {"description": "Find (active) installations for card", "http": {"path": "/cards/:cardId", "verb": "get"}, "accepts": [{"arg": "cardId", "type": "any", "http": {"source": "path"}, "required": true}], "returns": {"type": "array", "root": true}}, "findByCardIdBulk": {"description": "Find (active) installations for cards", "http": {"path": "/cards", "verb": "get"}, "accepts": [{"arg": "cardIds", "type": "array", "required": true}], "returns": {"type": "object", "root": true}}}}