{"name": "App", "plural": "Apps", "base": "Application", "idInjection": true, "strict": false, "mixins": {"MultitenantRemote": true}, "options": {}, "properties": {"id": {"type": "String", "id": true, "generated": true}}, "validations": [], "relations": {}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"sync": {"description": "Sync (App API)", "http": {"path": "/app/sync", "verb": "post"}, "accepts": [{"arg": "objectsUp", "type": "array", "required": true}, {"arg": "options", "type": "object"}], "returns": {"type": "object", "root": true}}}}