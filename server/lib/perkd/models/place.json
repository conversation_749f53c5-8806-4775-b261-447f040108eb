{"name": "Place", "plural": "Places", "base": "PersistedModel", "idInjection": true, "strict": false, "options": {"validateUpsert": true}, "mixins": {"MultitenantRemote": true, "Setting": true, "Errors": true}, "properties": {"id": {"type": "String", "id": true, "generated": true}, "createdAt": {"type": "date"}, "modifiedAt": {"type": "date"}, "deletedAt": {"type": "date"}}, "validations": [], "relations": {"phones": {"type": "embeds<PERSON><PERSON>", "model": "Phone", "property": "phoneList", "options": {"validate": true, "forceId": false, "persistent": true, "methods": ["default"]}}, "addresses": {"type": "embeds<PERSON><PERSON>", "model": "Address", "property": "addressList", "options": {"validate": true, "forceId": false, "persistent": true, "methods": ["default"]}}, "owner": {"type": "belongsTo", "model": "Business", "foreignKey": "ownerId"}, "photos": {"type": "hasMany", "model": "PhotoImage", "foreignKey": "ownerId"}, "visits": {"type": "hasMany", "model": "Visit", "foreignKey": "placeId"}, "settings": {"type": "embeds<PERSON><PERSON>", "model": "Setting", "property": "settingList", "options": {"validate": false, "forceId": false}}, "beacons": {"type": "referencesMany", "model": "Beacon", "foreignKey": "beaconIds"}, "card": {"type": "belongsTo", "model": "Card", "foreignKey": "cardId"}, "person": {"type": "belongsTo", "model": "Person", "foreignKey": "personId"}, "official": {"type": "belongsTo", "model": "Place", "foreignKey": "officialId"}}, "acls": [], "indexes": {}, "scopes": {}, "methods": {"fetchByGroup": {"description": "Fetch places by GroupId (Custom Place)", "http": {"path": "/fetchByGroup", "verb": "get"}, "accepts": [{"arg": "groupId", "type": "String", "required": true}], "returns": {"type": "array", "root": true}}, "findPlaceName": {"description": "Find place name (aggregated) nearby coordinates  (Custom Place)", "http": {"path": "/findPlaceName", "verb": "get"}, "accepts": [{"arg": "coordinates", "type": "String", "required": true}], "returns": {"type": "object", "root": true}}, "groupByBrand": {"description": "Aggregate count of each brand  (Custom Place)", "http": {"verb": "post", "path": "/groupByBrand"}, "accepts": [{"arg": "ownerId", "type": "any", "required": true}], "returns": {"arg": "result", "type": "object", "root": true}}, "syncFetch": {"description": "Used by Sync to fetch instances", "http": {"path": "/sync/fetch", "verb": "get"}, "accepts": [{"arg": "personId", "type": "string", "required": true}, {"arg": "last", "type": "date"}], "returns": {"type": ["Place"], "root": true}}, "prototype.getCardMasters": {"description": "Fetch Cardmasters linked through Business id", "http": {"path": "/cardmasters", "verb": "get"}, "accepts": [], "returns": {"type": "array", "root": true}}, "prototype.matchOfficial": {"description": "Match custom place with official places", "http": {"path": "/matchOfficial", "verb": "post"}, "accepts": [{"arg": "customPlace", "type": "Object", "required": true}], "returns": {"type": "array", "root": true}}}}