{"issue": "You have a new card", "share": "{{- card.sharer.name}} just shared a card with you! 💛", "share_f": "{{- card.sharer.name}} just shared a card she likes with you! 💛", "share_m": "{{- card.sharer.name}} just shared a card he likes with you! 💛", "shareConflict": "{{- sharing.recipient.name}} already has the {{- name}} 👻\nYour card was not shared", "shareReceived": "{{- sharing.recipient.name}} received the card that you shared 😎", "shareAccepted": "{{- sharing.recipient.name}} accepted the {{- name}} that you shared 😎", "shareDeclined": "Uh oh, {{- sharing.recipient.name}} declined the {{- name}} that you shared 💔", "shareUpgrade": "{{- card.sharer.name}} just shared {{- name}} with you! \nYou’ll need the latest app to receive it, please upgrade now 🚀", "remindExpiry": "(0)<Your {{- name}} is expiring today>;(1)<Your {{- name}} is expiring tomorrow>;(2-inf)<Your {{- name}} is expiring in {{- count}} days>;", "remindRegister": "(0)<Reminder💡: you have yet to register the {{- name}} you received today>;(1)<Reminder💡: you have yet to register the {{- name}} you received yesterday>;(2-inf)<Reminder💡: you have yet to register the {{- name}} you received {{- count}} days ago>;", "changeMaster": "Your {{brand.short}} card has been updated 💛", "changeMaster_1": "Your {{brand1}} card has been updated 💛", "changeMaster_2": "Your {{brand1}} & {{brand2}} cards are updated 💛", "changeMaster_3": "Your {{brand1}}, {{brand2}} & {{brand3}} cards are updated 💛", "changeMaster_4": "Your {{brand1}}, {{brand2}}, {{brand3}} & other cards are updated 💛", "migrate": "Your {{brand.short}} membership card has been moved to your new account"}