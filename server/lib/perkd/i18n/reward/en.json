{"issue_one": "Congrats!🎉 You have earned a new stamp", "issue_other": "Congrats!🎉 You have earned {{count}} new stamps", "remindExpiry": "(0)<Your have $t(quantity, {'count': {{quantity}} }) expiring today>;(1)<You have $t(quantity, {'count': {{quantity}} }) expiring tomorrow>;(2-inf)<You have $t(quantity, {'count': {{quantity}} }) expiring in {{count}} days>;", "quantity": "a reward", "quantity_plural": "{{count}} rewards"}