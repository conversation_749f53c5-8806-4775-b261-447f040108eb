/**
 *  @module Mixin:Sync - for resource type (eg. <PERSON><PERSON><PERSON><PERSON>, Account, PlaceList)
 */
const assert = require('node:assert'),
	{ Modules } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ isPre7app, syncUntilOf } = require('@perkd/sync')

const { SYNC } = Modules.Type
const FETCH_WAIT = 50
const mixinTips = (mixinName, modelName) => `[Sync mixin] require '${mixinName}' mixin on ${modelName} model.`

module.exports = function(Model) {

	assert(Model.definition.settings.mixins['CacheEvents'], mixinTips('CacheEvents', Model.name))

	/**
	 * Sync API (app)
	 * @param	{Object[]} objectsUp - Objects to sync up to server
	 * @param	{Number} syncUntil - Timestamp to sync from
	 * @param	{Number} [limit] - Maximum number of objects to fetch
	 * @param	{Boolean} [background] - Whether to run in background
	 * @param	{Object} options - backward compatible (pre 7.0)
	 *          {Number} fetchLimit - max. number of objects to sync down
	 *          {Number} syncUntil - override cache sync stat
	 * @param	{Request} req
	 * @param	{Response} res
	 * @return	{Object} { objects: [], syncUntil: timestamp(ms), partial }
	 */
	Model.appSync = async function(objectsUp = [], syncUntil, limit, background, options = {}, req, res) {
		const { name, syncUpdate } = Model,
			syncModule = appModule(SYNC),
			sync = syncModule.get(name),
			{ cached } = syncModule.settings(name),
			after = syncUntil || options.syncUntil || 0,
			max = limit || options.fetchLimit,
			{ user, installation } = Context,
			{ personId } = user || {},
			{ id: deviceId } = installation || {}

		// Validate required context
		if (!personId || !deviceId) {
			throw { statusCode: 401, message: 'Unauthorized' }
		}

		if (objectsUp.length && syncUpdate) {
			await syncUpdate(objectsUp, installation, user).catch(err => {
				appNotify('[Sync]syncUpdate:', { objectsUp, err })
			})
		}

		let result

		if (cached) {
			// FIXME Wait for cache to be updated
			await new Promise(resolve => setTimeout(resolve, FETCH_WAIT))

			// Fetch latest data from cache
			result = await sync.fetch(after, max, { ...options, install: installation })
		}
		else {
			const instances = Model.syncFetch ? await Model.syncFetch(personId, after).catch(() => []) : [], // some models (appEvent) don't need syncFetch but still return standard format
				legacy = isPre7app(installation),
				objects = sync.packInstances(instances, legacy),
				syncUntil = syncUntilOf(instances) || after,
				partial = instances.length === max

			result = { objects, syncUntil, partial }
		}

		sync.response(result, installation, res)
	}

	/**
	 * Fetch API (app)
	 * @param	{Object[]} objectIds
	 * @param	{Object} options - backward compatible (pre 7.0)
	 * @param	{Request} req
	 * @param	{Response} res
	 * @return	{Object} { objects: [] }
	 */
	Model.appFetch = async function(objectIds = [], options = {}, req, res) {
		const { name } = Model,
			sync = appModule(SYNC).get(name),
			{ installation } = Context,
			result = await sync.fetchByIds(objectIds, options)

		sync.response(result, installation, res)
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('appSync', {
		description: 'Sync (App API)',
		http: { path: '/app/sync', verb: 'post' },
		accepts: [
			{ arg: 'objectsUp', type: 'array', required: true },
			{ arg: 'syncUntil', type: 'number' },
			{ arg: 'limit', type: 'number' },
			{ arg: 'background', type: 'boolean' },
			{ arg: 'options', type: 'object' },
			{ arg: 'req', type: 'object', http: { source: 'req' } },
			{ arg: 'res', type: 'object', http: { source: 'res' } }
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('appFetch', {
		description: 'Fetch (App API)',
		http: { path: '/app/fetch', verb: 'post' },
		accepts: [
			{ arg: 'ids', type: 'array', required: true },
			{ arg: 'options', type: 'object' },
			{ arg: 'req', type: 'object', http: { source: 'req' } },
			{ arg: 'res', type: 'object', http: { source: 'res' } }
		],
		returns: { type: 'object', root: true },
	})
}
