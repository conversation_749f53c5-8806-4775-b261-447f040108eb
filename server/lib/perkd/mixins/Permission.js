/**
 *  @module Mixin:Permission
 */
const { Permissions, Contacts } = require('@crm/types'),
	{ EmbedLib } = appRequire('lib/common/embedLibrary')

const { Type: CHANNELS } = Permissions,
	{ ALLOWED, DENIED, UNKNOWN } = Permissions.Status,
	{ DONOTDISTURB } = Permissions.Options,
	{ MOBILE } = Contacts.Type,
	SUBMODELS = {
		mobile: 'Phone',
		email: 'Email',
		postal: 'Address',
	},
	EVENT = 'PERMISSIONCHANGED'

module.exports = function(Model, options = {}) {
	const permLib = new EmbedLib(Model, 'permissions', Object.assign({}, options, { event: EVENT }), 'channel')
	permLib.setup()

	// -----  Static Properties  -----
	Model.CHANNELS = options.channels || Object.keys(CHANNELS).map(key => CHANNELS[`${key}`])

	// -----  Instance Methods  -----

	Model.prototype._permission = function({ channel }) {
		return this.permissions.value().find(perm => perm.channel === channel)
	}

	// -----  Event Listeners  -----

	Model.CHANNELS.forEach(channel => {
		if (SUBMODELS[`${channel}`]) {
			Model.on(SUBMODELS[`${channel}`] + '.created', permissionUpdate.bind(channel))
			Model.on(SUBMODELS[`${channel}`] + '.changed', permissionUpdate.bind(channel))
		}
	})

	function permissionUpdate(evt) {
		const channel = this.toString(),
			instance = evt.instance,
			data = evt.data,
			options = Object.assign({}, evt.options),
			permission = instance._permission({ channel })

		delete options.before // evt is from phone/email/... update, should remove before key!

		if (permission.options.includes(DONOTDISTURB)) return // DND skip
		if (channel === CHANNELS.MOBILE && data.lineType !== MOBILE) return		// not mobile

		const status = data.optIn === true
			? ALLOWED
			: (data.optIn === false ? DENIED : UNKNOWN)

		if (status === permission.status || status === UNKNOWN) return		// no change

		instance.upsertPermissions({ channel, status }, options)
	}
}
