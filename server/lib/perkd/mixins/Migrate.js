/**
 *  @module Mixin:migrate - Perkd
 */

module.exports = function(Model) {

	/**
	 * Change owner of instance
	 * @param	{String} id of instance
	 * @param	{String} personId of new owner
	 * @param	{Object} options
	 *			{Object} through
	 * @return	{Object} - updated instance
	 */
	Model.migrate = async function(id, personId, options) {
		const instance = await Model.getInstance(id)
		return instance.migrate(personId, options)
	}

	/**
	 * Change owner of instance
	 * @param	{String} personId of new owner
	 * @param	{Object} options
	 *			{Boolean} shared
	 *			{Object} through
	 * @return	{object} - updated instance
	 */
	Model.prototype.migrate = async function(personId, options = {}) {
		const { app, name } = Model,
			from = this.personId,
			to = personId,
			{ through } = options

		if (to === from) return this.rejectErr('cannot_migrate_same_person', { from, to, options }, true)

		const instance = await this.updateAttributes({ personId: to }),
			model = name.toLowerCase()

		instance.emitEvent(app.Event[`${model}`].migrated, { from, to }, through)
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('migrate', {
		description: `Change owner (Person) of ${Model.name}`,
		http: { path: '/migrate', verb: 'post' },
		accepts: [
			{ arg: 'id', type: 'string', required: true, description: `${Model.name} instance id` },
			{ arg: 'personId', type: 'string', required: true, description: 'target person id' },
			{ arg: 'options', type: 'object', description: '{ through }' },
		],
		returns: { type: Model.name, root: true },
	})

	Model.remoteMethod('prototype.migrate', {
		description: `Change owner (Person) of ${Model.name}`,
		http: { path: '/migrate', verb: 'post' },
		accepts: [
			{ arg: 'personId', type: 'string', required: true, description: 'target person id' },
			{ arg: 'options', type: 'object', description: '{ through }' },
		],
		returns: { type: Model.name, root: true },
	})
}
