/**
 *  @module Mixin:Form Mixin
 */

const { getName } = require('@perkd/utils'),
	{ FORM: FORM_ERR } = require('@perkd/errors/dist/service')

const { FORM_INVALID, FORM_MISSING, FORM_CLEAN_ERROR } = FORM_ERR

module.exports = function(Model) {
	Model.prototype.validateForm = async function(name, data) {
		const { id } = this,
			{ name: model } = Model,
			form = this._form(name)

		if (form) {
			const errors = form.validate(data)
			return errors ? this.rejectErr(FORM_INVALID, errors) : data
		}

		return this.rejectErr(FORM_MISSING, { form: name, model, id }, true)
	}

	// -----  Private Static Methods  -----

	/**
	 * Cleanse name in formData
	 * @param	{Object} ctx
	 * @return	{Promise<Object>} mutated formData
	 */
	Model.cleanFormName = async function(ctx) {
		const { instance, data } = ctx,
			{ models, OSCAR } = Model.app,
			{ Person } = models,
			updates = instance || data,
			id = getLatest('id', ctx),
			personId = getLatest('personId', ctx),
			formData = getNew('formData', ctx) ?? {},
			prevFormData = getPrevious('formData', ctx) ?? {},
			{ familyName, givenName, displayName } = formData,
			skipClean = (formData && prevFormData)
				&& (!familyName && !givenName || (samePerson(prevFormData, formData, true) && displayName === prevFormData.displayName)),
			filter = { fields: [ 'familyName', 'givenName', 'fullName', 'name' ] }

		// card.edit skips clean as data is { $set: {'formData.familyName': ...} }
		if (skipClean) return formData

		const person = await Person.findById(personId, filter).catch(() => undefined),
			cleansed = samePerson(person, formData)
				? person
				: await OSCAR.clean({ familyName, givenName }).catch(() => undefined)

		if (!cleansed) {
			appNotify(FORM_CLEAN_ERROR, { id, formData, personId, prevFormData }, 'warn')
			return formData
		}

		const { familyName: fName, givenName: gName, name: { displayAs, order } } = cleansed

		updates.formData = {
			...formData,
			familyName: fName,
			givenName: gName,
			displayName: getName({ familyName: fName, givenName: gName }, displayAs || order),
			nameOrder: order,
		}
		return updates.formData
	}

	// -----  Private Instance Methods  -----

	Model.prototype._form = function(name) {
		return this.forms.find(form => form.name === name)
	}

	// -----  Private Functions  -----

	// TODO: move to utils/name
	function samePerson(a = {}, b = {}, exactOrder = false) {
		const
			{ familyName: aFName, givenName: aGName } = a || {},
			{ familyName: bFName, givenName: bGName } = b || {},
			sameFName = isEqualString(aFName, bFName),
			sameGName = isEqualString(aGName, bGName),
			isExact = sameFName && sameGName

		if (isExact) return true

		const
			matchFName = isEqualString(a.familyName, b.givenName),
			matchGName = isEqualString(a.givenName, b.familyName),
			isFlipped = matchFName && matchGName

		return !exactOrder ? isFlipped : false
	}

	// TODO: move to utils/string
	function isEqualString(a, b) {
		const areStrings = [ a, b ].every(n => typeof n === 'string')
		return (areStrings) ? a.toLowerCase() === b.toLowerCase() : a === b
	}

	// TODO: move to utils/hook
	function getPrevious(key, { instance, currentInstance = {} }) {
		return (instance) ? null : currentInstance[`${key}`]
	}

	function getLatest(key, { instance = {}, currentInstance = {}, data = {} }) {
		return instance[`${key}`] !== undefined ? instance[`${key}`]
			: data[`${key}`] !== undefined ? data[`${key}`]
				: currentInstance[`${key}`] !== undefined ? currentInstance[`${key}`] : null
	}

	function getNew(key, { instance = {}, data = {} }) {
		return data[`${key}`] !== undefined ? data[`${key}`]
			: instance[`${key}`] !== undefined ? instance[`${key}`] : null
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('prototype.validateForm', {
		description: 'Validate form data',
		http: { path: '/form/validate', verb: 'post' },
		accepts: [
			{ arg: 'name', type: 'string', required: true },
			{ arg: 'data', type: 'object', required: true },
		],
		returns: { type: 'object', root: true },
	})
}
