/**
 *  @module Mixin:Locale
 *
 * Time zone - https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
 */

const { isEmptyObj } = require('@perkd/utils')

module.exports = function(Model, options) {

	Model.prototype._locale = function() {
		const { locale } = this

		return locale && !isEmptyObj(locale) ? locale : null
	}

	// Model.prototype.defaultLanguage = function(language, options) {
	// 	const self = this;

	// 	// getter
	// 	if (!language) {
	// 		const result = (self._locale() && self.locale.languages) ?
	// 			(self.locale.languages.length > 0 ? self.locale.languages[0] : null) : null;

	// 		return Promise.resolve(result);
	// 	}
	// 	// setter
	// 	if (self._locale() && self.locale.languages) { // update existing;
	// 		const pos = self.locale.languages.findIndex(lang => (lang === language));
	// 		const moved = pos > 0 ? self.locale.languages.splice(pos, 1)[0] : language;

	// 		self.locale.languages.unshift(moved);
	// 		return self.updateAttributes({ locale: self.locale }).return(moved);
	// 	}
	// 	// create new;
	// 	if (self._locale()) { self._locale().languages = []; }

	// 	const defaultLoc = self._locale() || {
	// 		languages: [],
	// 		currency: '',
	// 		country: '',
	// 		timeZone: null,
	// 	};
	// 	defaultLoc.languages.push(language);
	// 	return self.upsertLocales(defaultLoc, options).return(language);
	// };
}
