/**
 *  @module Mixin:Share - Perkd
 */

const assert = require('node:assert'),
	{ pick } = require('lodash'),
	{ Persons, Wallet } = require('@crm/types'),
	{ propertiesOf } = require('@perkd/sync'),
	{ stepOf, nextStepOf, hasStep, parsePhoneNumber, getName, ObjectId } = require('@perkd/utils'),
	{ SHARE: SHARE_ERR, CARD: CARD_ERR } = require('@perkd/errors/dist/service')

const mixinTips = (mixinName, modelName) => `[Share mixin] require '${mixinName}' mixin on ${modelName} model, please config on top of 'Share'.`

const { Cards, Share } = Wallet,
	{ PENDING, ACTIVE, TRANSFERRING } = Cards.State,
	{ SHARE, SHAREORUSE } = Cards.Step,
	{ SIGNUP } = Cards.FlowName,
	{ INVITE, CLONE, SEND, TRANSFER, SUBOBJECT } = Share.Mode,
	{ PERKD, USER } = Persons.Identities,
	// errors
	{ SHARE_MODE_INVALID, SHARE_MODE_NOT_ALLOWED, SHARE_TARGET_MISSING, SHARE_MOBILE_INVALID, SHARE_PERSON_NOT_FOUND,
		SHARE_TARGET_CARD_MASTER_MISSING, SHARE_MODE_NOT_CONFIG, SHARE_MAXIMUM_LIMIT, SHARE_GENERATION_LIMIT,
		SHARE_ALREADY_HAVE, SHARE_ALREADY_TRANSFERRED, SHARE_TO_SELF, SHARE_RECIPIENT_NOT_FOUND } = SHARE_ERR,
	{ CARD_MAXIMUM_LIMIT } = CARD_ERR,
	//
	MODES = [ INVITE, CLONE, SEND, TRANSFER, SUBOBJECT ],
	CARD = 'Card',	// model name
	RETURN_PROPS = {
		Card: [ 'id', 'personId', 'masterId', 'preIssued', 'state', 'flow', 'sharings', 'createdAt', 'modifiedAt', '_model' ],
		Offer: [ 'id', 'personId', 'masterId', 'cardId', 'state', 'kind', 'sharings', 'applet', 'style', 'fields', 'venue', 'checkin', 'createdAt', 'modifiedAt', '_model' ],
	}

module.exports = function(Model) {
	// ----- assert dependencies ----
	assert(typeof Model.prototype.rejectErr === 'function', mixinTips('Errors', Model.name))
	assert(Array.isArray(RETURN_PROPS[Model.name]), `[Share mixin] RETURN_PROPS[${Model.name}] not config!`)

	// -----  Instance Methods  -----

	/**
	 * Share instance to recipient via mode
	 * @param	{Object} to - personId (or) mobile
	 * 			{String} personId
	 * 			{String} mobile
	 * 			{String} name
	 * @param	{String} mode - of sharing: invite, clone, transfer, send
	 * @param	{Object} options
	 * 			{String} channel
	 * @return	{Shared} shared instance
	 */
	Model.prototype.share = async function(to, mode, options = {}) {
		const { id, state } = this,
			{ channel } = options,
			{ personId, mobile } = to,
			through = {
				type: PERKD,
				format: SHARE,
				attributedTo: { type: USER, id: this.personId },
				touchedAt: new Date(),
			},
			canShare = await this.canShare(mode),
			EVENT = Model.app.Event[Model.name.toLowerCase()]

		try {
			if (!MODES.includes(mode)) return this.rejectErr(SHARE_MODE_INVALID, { mode }, true)
			if (!canShare) return this.rejectErr(SHARE_MODE_NOT_ALLOWED, { mode }, true)
			if (!(personId || mobile)) return this.rejectErr(SHARE_TARGET_MISSING, { personId, mobile }, true)
			if (!personId && mode === SEND) return this.rejectErr(SHARE_RECIPIENT_NOT_FOUND, { mobile }, true)

			if (!personId) {
				const cleansedMobile = await cleanMobile(this.personId, mobile)

				if (!cleansedMobile) {
					return this.rejectErr(SHARE_MOBILE_INVALID, { mobile })
				}
				to.mobile = cleansedMobile
				to.name = to.name || `+${cleansedMobile}`
			}

			if (mode === TRANSFER || (Model.name !== CARD && mode === SUBOBJECT)) {	// must ensure atomic
				const updated = await Model.findOneAndUpdate(
					{ id, state: ACTIVE },
					{ $set: { state: TRANSFERRING } }
				)

				if (!updated) throw SHARE_MODE_NOT_ALLOWED
			}

			const sharer = await this.buildSharer(mode),
				[ controller, recipient ] = await Promise.all([
					this.qualifyShare(mode),
					findOrCreatePerson(to)
				])

			if (!recipient) throw SHARE_PERSON_NOT_FOUND
			if (recipient.id === this.personId) throw SHARE_TO_SELF

			const source = this.toJSON(),
				card = await this.findOrIssueCard(recipient.id, to.name),
				shared = await controller.createShared(mode, recipient.id, card, source, sharer, to, options),
				sharing = await this.createSharing(mode, shared, recipient, sharer, channel)

			this.emitEvent		// FIXME @zhangli
				? this.emitEvent(EVENT.shared, { sharing }, through)
				: appEmit(EVENT.shared, this, { context: { sharing } }, { through })

			return shared
		}
		catch (error) {
			await this.updateAttributes({ state })	// restore state

			const code = (typeof error === 'string')
				? error
				: (error.code === CARD_MAXIMUM_LIMIT ? SHARE_ALREADY_HAVE : error.code)

			this.emitEvent		// FIXME @zhangli
				? this.emitEvent(EVENT.error.shared, { error }, through)
				: appEmit(EVENT.error.shared, this, { context: { error } }, { through })

			return this.rejectErr(code, { recipient: to })
		}
	}

	/**
	 * Cancel share
	 * @param	{String} sharingId - of sharing
	 * @return	{Object} syncDown
	 */
	Model.prototype.cancelShare = async function(sharingId) {
		const { id } = this,
			{ models, Event } = Model.app,
			{ Sharing } = models,
			evtName = Event[Model.name.toLowerCase()].shareCancelled,
			filter = {
				id: sharingId,
				'when.accepted': null,
				'when.cancelled': null
			},
			updates = { $set: { 'when.cancelled': new Date() } },
			sharing = await Sharing.findOneAndUpdate(filter, updates)

		if (!sharing) {		// update failed, imply card already accepted
			return this.rejectErr(SHARE_ALREADY_TRANSFERRED, {}, true)
		}

		await this.cancelShared(sharing)

		// non-card should be recalled via CRM
		const recalled = (Model.name === CARD)
				? await this.recall()
				: await Model.findById(id),
			syncDown = { sync: [ propertiesOf(recalled) ] }

		this.emitEvent		// FIXME @zhangli
			? this.emitEvent(evtName, { sharing })
			: appEmit(evtName, this, { context: { sharing } })

		return syncDown
	}

	/**
	 * Share instance to recipients via mode
	 * @param	{Object[]} recipients - mutated
	 * 			{Object} { personId|mobile, name }
	 * @param	{String} mode - of sharing: invite, clone, transfer, send
	 * @param	{Object} options
	 * 			{String} channel
	 * @return	{Object} { recipients, sync: { Card:[] } }
	 */
	Model.prototype.shareToMany = async function(recipients = [], mode, options) {
		const { id } = this,
			canShare = await this.canShare(mode)

		if (!MODES.includes(mode)) return this.rejectErr(SHARE_MODE_INVALID, { mode }, true)
		if (!canShare) return this.rejectErr(SHARE_MODE_NOT_ALLOWED, { mode }, true)

		for (const recipient of recipients) {
			try {
				await this.share(recipient, mode, options)
				recipient.shared = true
			}
			catch ({ code }) {
				recipient.shared = false
				recipient.reason = { code }
			}
		}

		// must refetch, self updated indirectly in share()
		const filter = {
				include: [ {
					relation: 'sharings',
					scope: {
						where: { mode: TRANSFER, 'when.cancelled': null },
					}
				} ]
			},
			updated = await Model.findById(id, filter)

		if (Model.name === CARD && !recipients.find(r => !r.shared)) {
			const flow = updated.flow?.toJSON()

			if (flow && hasStep(SHARE, flow)) {
				flow.at = nextStepOf(SHARE, flow)
				await updated.updateAttributes({ flow })
			}
		}

		const obj = propertiesOf(updated),
			objects = [ pick(obj, RETURN_PROPS[Model.name]) ]

		return { recipients, sync: objects }
	}

	// -----  Private Methods  -----

	/**
	 * Enforce share policies and limits, update count and remain  (defined in 'sharePolicies' property)
	 * @param	{String} mode
	 * @return	{Instance} controller - instance with 'sharePolicies' property
	 */
	Model.prototype.qualifyShare = async function(mode) {
		if (Model.name !== CARD) return this	// non-cards qualified by CRM

		// Enforce CARD sharing policies
		const { sharer } = this,
			controller = await this.cardMaster.get(),
			ndx = controller.sharePolicies.findIndex(policy => policy.mode === mode),
			policy = ndx >= 0 ? controller.sharePolicies[`${ndx}`] : null,
			nextGeneration = sharer ? sharer.generation + 1 : 1,
			changes = {
				$inc: { [`sharePolicies.${ndx}.count`]: 1 },
			}

		if (!policy) {
			return this.rejectErr(SHARE_MODE_NOT_CONFIG, { mode }, true)
		}

		const { limit, remain, max } = policy

		if (limit) {
			if (remain === 0) {
				return this.rejectErr(SHARE_MAXIMUM_LIMIT, { policy }, true)
			}
			changes.$inc[`sharePolicies.${ndx}.remain`] = -1
		}
		if (max.generation && max.generation < nextGeneration) {
			return this.rejectErr(SHARE_GENERATION_LIMIT, { policy }, true)
		}

		return controller.constructor.findOneAndUpdate({ id: controller.id }, changes)
	}

	/**
	 * (SAMPLE) Create shared object (implemented by 'controller' of each sharable model)
	 * @param	{String} mode
	 * @param	{String} personId
	 * @param	{String} cardId of receiving card (optional)
	 * @param	{Object} source - originating object
	 * @param	{Object} sharer
	 * @param	{Object} options
	 * @return	{Instance}
	 */
	// Model.prototype.createShared = function(mode, personId, cardId, source, sharer, options) {
	// 	return Promise.reject('createShared() must be implemented by controller model');
	// };

	/**
	 * Determine if share mode is allowed	(implemented for non-card instances: offer, reward, messsage)
	 * @param	{String} mode of sharing: invite, clone, transfer
	 * @return	{Boolean}
	 */
	if (Model.name !== CARD) {
		Model.prototype.canShare = async function(mode) {
			return !!(Array.isArray(this.shareModes) && this.shareModes.includes(mode))
		}
	}

	Model.prototype.buildSharer = async function(mode) {
		const { id, personId } = this,
			[ person, imageUrl ] = await Promise.all([
				this.person.get(),
				getProfileImageUrl(personId)
			]),
			name = person
				? getName(person, person.name?.displayAs) || person.fullName
				: '',
			sharer = {
				name,
				imageUrl,
				personId: personId.toString(),
				mode: mode === SUBOBJECT ? INVITE : mode,
				originId: id.toString(),
				sharingId: new ObjectId().toString(),	// for sharing creation
				generation: this.sharer?.generation + 1 || 1,
				noNotify: mode === SUBOBJECT,
			}

		return sharer
	}

	/**
	 * Find or issue receiving card   (for NON-CARD sharing only)
	 * @param	{String} personId of recipient
	 * @param	{String} name of recipient
	 * @return	{Card}
	 */
	Model.prototype.findOrIssueCard = async function(personId, name) {
		if (Model.name === CARD) return		// no receiving card when sharing card

		const { cardId } = this,
			{ Card } = Model.app.models,
			include = {
				include: {
					relation: 'cardMaster',
					scope: { fields: [ 'sharePolicies' ] }
				}
			},
			sharerCard = await Card.findById(cardId, include),
			sharePolicy = sharerCard?.cardMaster.sharePolicies.find(policy => policy.mode === SUBOBJECT),
			toCardMasterIds = sharePolicy?.toCardMasterIds || [ sharerCard?.masterId ]

		if (!toCardMasterIds.length) {
			return this.rejectErr(SHARE_TARGET_CARD_MASTER_MISSING, { personId, name }, true)
		}

		const filter = {
				where: {
					masterId: { inq: toCardMasterIds },
					personId,
					state: { inq: [ ACTIVE, PENDING ] },
					'when.terminated': null,
					'when.cancelled': null,
					'when.revoked': null,
				},
				order: 'state ASC'
			},
			recipientCard = await Card.findOne(filter),
			{ deletedAt, hiddenAt } = recipientCard || {}

		return recipientCard
			? deletedAt || hiddenAt ? recipientCard.recover() : recipientCard
			: sharerCard.share({ personId, name }, SUBOBJECT)
	}

	/**
	 * Create sharing transaction
	 * @param	{String} mode of sharing
	 * @param	{Object} shared instance - 'null' when sharing non-card & pending (ie. queued in CRM)
	 * @param	{Person} person recipient
	 * @param	{Sharer} sharer
	 * @param	{String} channel
	 * @return	{Object} shared instance - null if pending
	 */
	Model.prototype.createSharing = async function(mode, shared, person, sharer, channel) {
		const { id: sharedId } = shared || {},
			{ id: personId, fullName, givenName, phoneList } = person,
			name = fullName || givenName,
			mobile = phoneList[0]?.fullNumber || '',
			{ sharingId, originId, generation } = sharer,
			imageUrl = await getProfileImageUrl(personId),
			sharing = {
				id: sharingId,
				mode: mode === SUBOBJECT ? INVITE : mode,
				recipient: {
					personId,
					mobile,
					name: name ? name.slice(0, 80) : `+${mobile}`,
					imageUrl
				},
				originId,
				sharedId,
				generation,
				channel,
				purgeTime: null,		// FIXME: need to come from CRM?
				when: shared ? { received: new Date() } : {},
			}

		return this.sharings.create(sharing)
	}

	/**
	 * Recall this (shared) object
	 * @param	{Object} options
	 *			{String} action
	 *			{String} sharingId
	 * @return {Object} this
	 */
	Model.prototype.recall = async function(options = {}) {
		const { action } = options,
			when = this.when.toJSON(),
			state = ACTIVE

		when.transferred = null

		if (Model.name === CARD) {
			const cardmaster = await this.cardMaster.get(),
				flow = cardmaster.flows[SIGNUP]

			if (flow && (hasStep(SHARE, flow) || hasStep(SHAREORUSE, flow))) {
				const share = stepOf(SHARE, flow),
					shareOrUse = stepOf(SHAREORUSE, flow),
					at = Math.max(share, shareOrUse)

				flow.at = at
				return this.updateAttributes({ state, flow, when })
			}
		}

		const updated = await this.updateAttributes({ state, when })

		if (action) {
			this.notify({ action }, options)
				.catch(err => appNotify('[shareRecallNotify]', { err, action }))
		}
		return updated
	}

	// -----  Private functions  -----

	// find or create (target) Person, inject name as fullName if absent
	async function findOrCreatePerson({ personId, mobile, name }) {
		const { Person } = Model.app.models,
			person = personId
				? await Person.findById(personId)
				: await Person.findOrCreateByMobile(mobile)

		person.fullName = person.fullName || name
		return person
	}

	// cleanse mobile using person's country code as default
	async function cleanMobile(personId, mobile) {
		const { Person } = Model.app.models,
			person = await Person.findById(personId, { fields: { phoneList: true } }),
			{ countryCode } = person.phoneList[0] || {},
			parsed = parsePhoneNumber(mobile, mobile.includes('+') ? '' : countryCode), // alternative mode
			{ isPossibleMobile, fullNumber } = parsed

		return isPossibleMobile ? fullNumber : ''
	}

	/**
	 * @param	{String} ownerId - personId
	 * @return	{Promise<String|Null>} - imageUrl
	 */
	async function getProfileImageUrl(ownerId = '') {
		const { ProfileImage } = Model.app.models

		return ProfileImage.getUrlByOwnerId(String(ownerId))
			.catch(() => null)
	}

	// -----  Remote & Operation hooks  -----

	Model.observe('before save', async ({ isNewInstance, instance, hookState }) => {
		const { Sharing } = Model.app.models

		if (isNewInstance) {
			const { sharer } = instance,
				{ sharingId, originId } = sharer || {}

			if (sharer && sharingId && !originId) {
				hookState.sharingId = sharingId

				const sharing = await Sharing.findById(sharingId)
				if (sharing) {
					const { personId, originId } = sharing
					instance.sharer = { ...sharer, personId, originId }
				}
			}
		}
	})

	Model.observe('after save', async ({ instance, hookState }) => {
		const { Sharing } = Model.app.models,
			{ sharingId } = hookState

		if (instance && sharingId) {
			const sharing = await Sharing.findById(sharingId)
			if (sharing) {
				await sharing.updateAttributes({ sharedId: instance.id })
			}
		}
	})

	// -----  Remote Methods  -----

	Model.remoteMethod('prototype.share', {
		description: 'Share (app API)',
		http: { path: '/app/share', verb: 'post' },
		accepts: [
			{ arg: 'recipient', type: 'object', required: true, description: '{ personId, mobile }' },
			{ arg: 'mode', type: 'string', required: true, enum: [ 'invite', 'clone', 'transfer', 'send' ] },
			{ arg: 'options', type: 'object', description: '{ channel }' },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.shareToMany', {
		description: 'Share (app API)',
		http: { path: '/app/shareToMany', verb: 'post' },
		accepts: [
			{ arg: 'recipients', type: 'array', required: true, description: '[{ personId, mobile, name }]' },
			{ arg: 'mode', type: 'string', required: true, enum: [ 'invite', 'clone', 'transfer', 'send' ] },
			{ arg: 'options', type: 'object', description: '{ channel }' },
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.cancelShare', {
		description: 'Cancel Share (app API)',
		http: { path: '/app/share/cancel', verb: 'post' },
		accepts: [
			{ arg: 'sharingId', type: 'string', required: true },
		],
		returns: { type: 'object', root: true },
	})
}
