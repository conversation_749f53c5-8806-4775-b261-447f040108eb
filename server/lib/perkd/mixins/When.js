/**
 *  @module Mixin:When - update when from app and auto-trigger event
 */
const { pickObj, isEmptyObj } = require('@perkd/utils')

module.exports = function(Model) {

	Model.appWhen = async function(ids, when) {
		const { name: _model } = Model,
			filter = {
				where: { id: { inq: ids } }
			},
			instances = await Model.find(filter),
			res = { sync: [] }

		for (const instance of instances) {
			const { id, personId, cardId, masterId, when, state, createdAt, modifiedAt } = instance.toJSON()
			res.sync.push({ id, personId, cardId, masterId, when, state, createdAt, modifiedAt, _model })
		}

		if (instances.length < ids.length) {
			const missing = ids.filter(id => !res.sync.some(i => i.id === id))
			appNotify('appWhen/not_found', { model: _model, missing })
		}

		return res
	}

	// -----  Instance Methods  -----

	Model.prototype.appWhen = async function(when) {
		const { Event } = Model.app,
			count = 1,
			before = this.when.toJSON(),
			change = Object.keys(before).reduce((res, key) => {
				if (!before[key] && when[key]) res[key] = when[key]
				return res
			}, {})

		if (isEmptyObj(change)) return this

		const updated = await this.updateAttributes({ when: { ...before, ...change } }),
			data = updated.toJSON(),
			{ when: after } = data

		for (const key of Object.keys(before)) {
			if (!before[key] && after[key]) {
				const eventData = pickObj(data, Model.EVENT_PROPERTIES)

				// TODO: move to richmessage.js (i.e. message tracking events)
				if (Model.name === 'Message') {
					if (eventData.track && !eventData.track.personId) { // patch missed personId
						eventData.track.personId = eventData.track.person && eventData.track.person.id
					}
					eventData.person = { id: eventData.track.personId }
					eventData.tag = eventData.track
				}

				appEmit(Event[Model.name.toLowerCase()][key], eventData, { when, count })
			}
		}

		return updated
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('appWhen', {
		description: 'Update instances when (App API)',
		http: { path: '/app/when', verb: 'post' },
		accepts: [
			{ arg: 'ids', type: 'array', required: true },
			{ arg: 'when', type: 'object', required: true },
		],
		returns: { type: 'object', root: true },
	})
}
