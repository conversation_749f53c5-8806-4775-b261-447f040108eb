/**
 *  @module Mixin:CacheEvents - for models supporting Sync
 * 	  - sync event handlers - writes server-side changes to cache (redis or memory)
 */
const { Modules } = require('@crm/types')

const { SYNC } = Modules.Type

module.exports = function(Model, options = {}) {

	const model = Model.name.toLowerCase(),
		{ service = model } = options,
		Event = {
			object: {
				created: `${service}.${model}.created`,
				updated: `${service}.${model}.updated`,
				deleted: `${service}.${model}.deleted`
			}
		}

	appOn(Event.object.created, async evt => {
		try {
			const { name } = Model,
				syncModule = appModule(SYNC),
				sync = syncModule.get(name),
				{ cached, changes } = syncModule.settings(name),
				{ id, personId = id, createdAt, purgeTime } = evt

			await Promise.all([
				cached ? sync.addInstance(evt) : null,
				changes ? sync.changes.objectCreated(personId, model, String(id), createdAt, purgeTime) : null
			])
		}
		catch (err) {
			console.error('[CacheEvents] object.created event:', { err, evt })
		}
	})

	appOn(Event.object.updated, async evt => {
		try {
			const { name } = Model,
				syncModule = appModule(SYNC),
				sync = syncModule.get(name),
				{ cached, changes } = syncModule.settings(name),
				{ id, personId = id, modifiedAt, context = {}, purgeTime } = evt,
				{ delta = [] } = context

			await Promise.all([
				cached ? sync.updateInstance(evt) : null,
				changes ? sync.changes.objectUpdated(personId, model, String(id), delta, modifiedAt, purgeTime) : null
			])
		}
		catch (err) {
			console.error('[CacheEvents] object.updated event:', { err, evt })
		}
	})

	appOn(Event.object.deleted, async evt => {
		try {
			const { name } = Model,
				syncModule = appModule(SYNC),
				sync = syncModule.get(name),
				{ cached, changes } = syncModule.settings(name),
				{ id, personId = id, deletedAt, purgeTime } = evt

			await Promise.all([
				cached ? sync.removeInstance(id) : null,
				changes ? sync.changes.objectDeleted(personId, model, String(id), deletedAt, purgeTime) : null
			])
		}
		catch (err) {
			console.error('[CacheEvents] object.deleted event:', { err, evt })
		}
	})
}
