/**
 *  @module Mixin:Cache - for Sync 3.0: distributed cache
 */
const assert = require('node:assert'),
	{ Modules, Touchpoints } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ propertiesOf, isPre7app, updates<PERSON>rom<PERSON>hanges, syncUntilOf } = require('@perkd/sync')

const { SYNC } = Modules.Type,
	{ PERKD } = Touchpoints.Type,
	{ CACHE } = Touchpoints.Format,
	{ INSTALLATION } = Touchpoints.Instrument

const mixinTips = (mixinName, modelName) => `[Cache mixin] require '${mixinName}' mixin on ${modelName} model.`

module.exports = function(Model) {

	assert(Model.definition.settings.mixins['CacheEvents'], mixinTips('CacheEvents', Model.name))

	/**
	 * Sync API (app)
	 * @param	{Object[]} objectsUp
	 * @param	{Number} syncUntil
	 * @param	{Number} [limit]
	 * @param	{<PERSON><PERSON><PERSON>} [background]
	 * @param	{Object} options - backward compatible (pre 7.0)
	 *          {Number} fetchLimit - max. number of objests to sync down
	 *          {Number} syncUntil - override cache sync stat
	 * @param	{Request} req
	 * @param	{Response} res
	 * @return	{Object} { objects: [], syncUntil: timestamp(ms), partial }
	 */
	Model.appSync = async function (objectsUp = [], syncUntil, limit, background, options = {}, req, res) {
		const last = syncUntil || options.syncUntil,
			max = limit || options.fetchLimit,
			{ user, installation } = Context,
			{ personId } = user,
			{ id: deviceId } = installation,
			legacy = isPre7app(installation)

		if (!personId || !deviceId) {
			appNotify('[Cache]sync - missing personId or deviceId', { personId, deviceId, objectsUp })
			return
		}

		if (legacy) { // compat for pre 7.0, convert Preference to AccountSetting
			const index = objectsUp.findIndex(o => o._model === 'Preference')
			if (index !== -1) objectsUp[index]._model = 'AccountSetting'
		}

		const updates = await Model.cacheUpdate(objectsUp, personId, deviceId)

		// TODO queue requests by personId?
		await Model.cacheFetch(personId, last, max, installation, updates, res)
	}

	/**
	 * Fetch API (app)
	 * @param	{Object[]} ids
	 * @param	{Object} options - backward compatible (pre 7.0)
	 * @param	{Request} req
	 * @param	{Response} res
	 * @return	{Object} { objects: [] }
	 */
	Model.appFetch = async function (ids = [], options = {}, req, res) {
		const { personId = options.personId } = Context.user ?? {},
			instances = await Model.syncFetch(personId, undefined, ids),
			objects = instances.map(i => propertiesOf(i))

		return { sync: objects }
	}

	/**
	 * Combine & apply changes from app
	 * @param	{Object[]} objectsUp
	 * @param	{String} personId
	 * @param	{String} deviceId
	 * @return	{Promise<Object[]>} objects to sync down to app
	 */
	Model.cacheUpdate = async function (objectsUp = [], personId, deviceId) {
		const { sync } = appModule(SYNC),
			name = Model.name.toLowerCase(),
			changes = await sync.changes.getAll(personId, deviceId, true).catch(() => ({})),
			exclude = [],
			updated = []

		for (const { _model, ...update } of objectsUp) {
			const { id, createdAt, modifiedAt } = update,
				modified = !!modifiedAt,
				created = !modified && !!createdAt,
				{ changes: serverChanges } = changes[name]?.find(c => c.id === id) ?? {}

			update.through = {
				type: PERKD,
				format: CACHE,
				instrument: {
					type: INSTALLATION,
					id: deviceId
				},
				touchedAt: new Date(),
			}

			if (created) {
				try {
					Model.create(update).catch(err => appNotify(`[${name}]syncUpdate.create`, { err, personId, update }, 'error'))
					exclude.push(id)
				}
				catch (err) {
					appNotify(`[${name}]syncUpdate.create`, { err, personId, update }, 'error')
					exclude.push(id)
				}
			}
			else if (!serverChanges?.length) {
				// only app changes
				try {
					Model.upsert(update).catch(err => appNotify(`[${name}]syncUpdate.upsert`, { err, personId, update }, 'error'))
					exclude.push(id)
				}
				catch (error) {
					appNotify(`[${name}]syncUpdate.upsert`, { error, personId, update }, 'error')
					exclude.push(id)
				}
			}
			else {
				// both server & app changes
				const current = id
					? await Model.findById(id).catch(() => undefined)
					: await Model.findOne({ where: update }).catch(() => undefined)

				if (current) {
					const { server, app } = updatesFromChanges(current, serverChanges, update)
					// createdAt & modifiedAt should be set automatically by Timestamp mixin
					delete server.createdAt
					delete server.modifiedAt
					await current.updateAttributes(server)
					updated.push(app)
				}
			}

			if (Model.syncUpdate) {	// model specific second-stage updates
				Model.syncUpdate(update, personId, deviceId)
					.catch(err => appNotify(`[${Model.name}]syncUpdate`, { err, personId, update }, 'error'))
			}
		}

		return { updated, exclude }
	}

	/**
	 * Fetch objects for model to sync down to app
	 * @param	{String} personId
	 * @param	{Number|void} [syncUntil]
	 * @param	{Number|void} [limit] - not supported (for now)
	 * @param	{Object} install
	 * @param	{Object} updates
	 *				{String[]} exclude - omit from fetch (app already updated)
	 *				{Object[]} updated - changes for app
	 * @param	{ServerResponse} res
	 * @return	{Promise<Object[]>} objects to sync down to app
	 */
	Model.cacheFetch = async function (personId, syncUntil, limit, install, updates, res) {
		const syncModule = appModule(SYNC),
			{ sync } = syncModule,
			{ name } = Model,
			mSync = syncModule.get(name),
			legacy = isPre7app(install),
			last = syncUntil ? new Date(syncUntil) : undefined,
			head = legacy ? '{"objects":[' : undefined,
			stream = sync.streams.create(res, install),
			fetches = []

		stream.begin(head)

		if (Model.syncFetch) {
			fetches.push(
				Model.syncFetch(personId, last)
					.then(instances => {
						const syncUntil = syncUntilOf(instances),
							applied = Model.applyUpdates(instances, updates),
							objects = mSync.packInstances(applied, legacy)

						stream.objects(objects)
						return { syncUntil, count: objects.length }
					})
			)
		}

		const results = await Promise.all(fetches),
			{ syncUntil: until, count } = results.reduce((t, r) => {
				const count = t.count + r.count,
					syncUntil = r.syncUntil > t.syncUntil ? r.syncUntil : t.syncUntil

				return { syncUntil, count }
			}, { syncUntil: Date.now(), count: 0 }),
			partial = false,
			tail = legacy
				? `],"syncUntil":${until},"partial":${partial}}`
				: { objects: count, syncUntil: until, partial }

		stream.end(tail)
	}

	/**
	 * Apply updates to fetched instances
	 * @param	{Object[]} instances
	 * @param	{Object} updates
	 *				{String[]} exclude - omit from fetch (app already updated)
	 *				{Object[]} updated - changes for app
	 * @return	{Object[]}
	 */
	Model.applyUpdates = function (instances, updates) {
		const { exclude, updated } = updates,
			excluded = instances.reduce((res, i) => {
				if (!exclude.includes(String(i.id))) {
					res.push(i)
				}
				return res
			}, [])

		return excluded
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('appSync', {
		description: 'Sync (App API)',
		http: { path: '/app/sync', verb: 'post' },
		accepts: [
			{ arg: 'objectsUp', type: 'array', required: true },
			{ arg: 'syncUntil', type: 'number', description: 'timestamp (ms) of last sync' },
			{ arg: 'limit', type: 'number' },
			{ arg: 'background', type: 'boolean' },
			{ arg: 'options', type: 'object', description: 'backward compatible: { syncUntil, fetchLimit }' },
			{ arg: 'req', type: 'object', http: { source: 'req' } },
			{ arg: 'res', type: 'object', http: { source: 'res' } }
		],
		returns: { type: 'object', root: true, description: '{ objects: [], syncUntil, partial }' },
	})

	Model.remoteMethod('appFetch', {
		description: 'Fetch (App API)',
		http: { path: '/app/fetch', verb: 'get' },
		accepts: [
			{ arg: 'ids', type: 'array', required: true },
			{ arg: 'options', type: 'object' },
			{ arg: 'req', type: 'object', http: { source: 'req' } },
			{ arg: 'res', type: 'object', http: { source: 'res' } }
		],
		returns: { type: 'object', root: true },
	})
}
