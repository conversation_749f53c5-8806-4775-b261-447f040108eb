/**
 *  @module Mixin:Purge
 */

const { bm, startOf, endOf } = require('@perkd/utils')

const PURGE_HANDLER = 'purgeHandler'

module.exports = function(Model) {

	Model.handlePurge = async function(daysAgo = 1, eachSize = 500) {
		const startDay = startOf('day', -daysAgo),
			endOfYesterday = endOf('day', -1),
			filter = {
				where: {
					purgeTime: { between: [ startDay, endOfYesterday ] },
					or: [
						{ deletedAt: null },
						{ deletedAt: { exists: false } }
					]
				},
				limit: eachSize,
			},
			start = bm.mark(),
			res = await Model.countPurge(daysAgo),
			{ total } = res

		let remain = total,
			success = 0,
			failed = 0,
			processed = 0

		appNotify(PURGE_HANDLER, res, 'start')
		process().catch(err => console.log('Err [handlePurge]', err))

		return { state: 'started' }

		async function process() {
			const list = [],
				instances = await Model.find(filter)
					.catch(err => console.error('[handlePurge]', { err, filter }))

			appEcho('[Purge] processing...%d% (%d/%d)'.green, Math.round((processed / total) * 100), processed, total)

			for (const instance of instances || []) {
				remain--
				processed++
				list.push(
					instance.purge().catch(err => {
						appLog(PURGE_HANDLER, { instance, err })
						throw err
					})
				)
			}

			await Promise.allSettled(list).then(results => {
				for (const { status } of results) {
					if (status === 'fulfilled') success++
					else failed++
				}
			})

			if (remain > 0 && instances.length > 0) {
				setTimeout(() => process(), 3000)
			}
			else {
				const benchmark = bm.diff(start)
				appNotify(PURGE_HANDLER, { total, success, failed, benchmark }, failed ? 'error' : 'done')
			}
		}
	}

	Model.countPurge = async function(daysAgo) {
		const startDay = startOf('day', -daysAgo),
			endOfYesterday = endOf('day', -1),
			filter = {
				where: {
					purgeTime: { between: [ startDay, endOfYesterday ] },
					or: [
						{ deletedAt: null },
						{ deletedAt: { exists: false } }
					]
				},
				limit: 50,
			},
			total = await Model.count(filter.where)
				.catch(err => console.error('[countPurge]', { err, filter }))

		return { total, filter }
	}

	// -----  Instance Methods  -----

	Model.prototype.purge = async function(force) {
		const { purgeTime } = this,
			NOW = new Date()

		if (force || purgeTime && purgeTime <= NOW) {
			const changes = { deletedAt: NOW }

			if (force) (changes.purgeTime = NOW)
			await this.updateAttributes(changes)
			return { purged: true }
		}

		return { purged: false }
	}

	// -----  Remote Methods  -----

	Model.remoteMethod('handlePurge', {
		description: `Purge ${Model.name}`,
		http: { path: '/purge', verb: 'post' },
		accepts: [
			{
				arg: 'daysAgo',
				type: 'number',
				description: 'Days ago of purgeTime.',
				default: 1,
			},
			{
				arg: 'eachSize',
				type: 'number',
				description: `Batch size of ${Model.name} processing`,
				default: 500,
			},
		],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('countPurge', {
		description: `Count need to purge ${Model.name}`,
		http: { path: '/purge/count', verb: 'get' },
		accepts: [ { arg: 'daysAgo', type: 'number', description: 'Days ago of purgeTime.', default: 1 } ],
		returns: { type: 'object', root: true },
	})

	Model.remoteMethod('prototype.purge', {
		description: `Purge ${Model.name}`,
		http: { path: '/purge', verb: 'post' },
		accepts: [ { arg: 'force', type: 'boolean', default: false } ],
		returns: { type: 'object', root: true },
	})
}

/**
 * End of script
*/
