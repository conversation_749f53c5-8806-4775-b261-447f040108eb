/**
 *  @module Activity module
 */

const EventEmitter = require('node:events'),
	getValue = require('get-value').default,
	NodeCache = require('node-cache'),
	pLimit = require('p-limit'),
	{ pick2, delay, satisfyCondition } = require('@perkd/utils')

class ActivityModule extends EventEmitter {
	/**
	 * @param	{Object} app - emitter
	 * @param	{Object} settings
	 * 			{Boolean} enabled
	 * 			{Object} presets - from config/activity.json/presets
	 * 			{Object} options - { definitions }
	 */
	constructor(app, settings) {
		const { options = {}, presets = {}, enabled, definitions } = settings,
			{ maxListeners, stdTTL = 100, checkperiod = 120 } = options

		super()
		this.app = app
		this.settings = settings
		this.enabled = enabled
		this.options = options
		this.presets = presets
		this.definitions = definitions ? require(definitions)?.REGISTRY || {} : {}

		this.names = Object.keys(this.definitions)

		if (!this.names.length) {
			throw new Error('Activity definitions missing')
		}

		this.myCache = new NodeCache({ stdTTL, checkperiod })
		if (maxListeners) {
			this.setMaxListeners(maxListeners)
		}
	}

	async ready() {
		const { presets } = this

		// Limit concurrent preset processing to avoid blocking the event loop
		const limit = pLimit(5)

		// Process all presets with concurrency control
		await Promise.all(
			presets.map(name => limit(() => this.add(name)))
		)
	}

	addDefinitions(definitions = {}) {
		this.definitions = { ...this.definitions, ...definitions }
	}

	async add(name) {
		const self = this
		const { definitions, myCache } = this,
			DEFAULT = 1000,
			definition = definitions[name],
			filter = definition && definition.filter || null

		let event = definition && definition.event || null

		if (event) {
			if (!Array.isArray(event)) event = [ event ]

			for (const eventName of event) {
				this.on(eventName, async function(evt) {
					const { id, data, timestamp } = evt

					if (!satisfyCondition(data, filter)) return

					const { actor, dataPick, property, options = {} } = definition,
						{ merge, delay: waitTime = DEFAULT } = options,
						activity = pick2(data, dataPick),
						actorId = getValue(data, property)

					activity.name = name
					activity.event = id
					if (!activity.occurredAt) activity.occurredAt = timestamp ? new Date(timestamp) : new Date()

					if (!merge) {
						await self.save({ actor, actorId, data: activity })
						return
					}

					const cache = myCache.get(actorId)

					if (cache) {
						cache.push(activity)
						myCache.set(actorId, cache)
					}
					else myCache.set(actorId, [ activity ])

					await delay(waitTime)

					const refreshedCache = myCache.get(actorId)

					if (refreshedCache) {
						await self.mergeAndSave(actor, actorId, refreshedCache)
						myCache.del(actorId)
					}
				})
			}
		}
	}
	//  FIXME   bug???  which listener are we removing??
	// remove(name, cb) {
	// 	const { event } = this.definitions[name]

	// 	this.removeListener(event, () => {
	// 		cb && cb()
	// 	})
	// }

	async save(activity = {}) {
		const { models } = this.app,
			{ actor } = activity,
			Model = models[actor]

		try {
			return await Model.createActivity(activity)
		}
		catch (err) {
			// Log error but don't throw to prevent service disruption
			console.error('ERROR [saveActivity]', { err, activity })
			return null
		}
	}

	async mergeAndSave(actor, actorId, activities = []) {
		if (!activities.length) return

		const [ activity ] = activities,
			{ result } = activity

		for (let i = 1; i < activities.length; i++) {
			result.delta = result.delta.concat(activities[i].result && activities[i].result.delta || [])
		}

		this.save({ actor, actorId, data: activity })
	}
}

module.exports = exports = ActivityModule
