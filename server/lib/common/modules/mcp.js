/**
 *  @module MCP
 *
 *  Generic MCP module for all microservices
 *  - Provides Model Context Protocol (MCP) server functionality
 *  - Dynamically loads service-specific tools and resources
 */

const { MCPModule } = require('@perkd/mcp-core')
const { McpExtension } = require('../../../mcp/mcp-extension')

const STREAMABLE_HTTP = 'streamable-http'
const PROTOCOL_VERSION = '2025-06-18'

/**
 * MCP Module for CRM microservices
 * Extends the core MCPModule with Loopback-specific functionality
 */
class MCPServer extends MCPModule {
	/**
	 * @param {Object} app - Loopback application instance
	 * @param {Object} settings - Module settings
	 */
	constructor(app, settings) {
		super(app, settings)

		// Add Loopback-specific properties
		this.service = app.service
		this.mcpExtension = null
	}

	/**
	 * Override the initServiceExtensions method from the parent class
	 * This is called during init() in the parent class
	 */
	async initServiceExtensions() {
		const {
			transport = STREAMABLE_HTTP,
			protocolVersion = PROTOCOL_VERSION,
			security,
			logging,
			bodyParser,
			session
		} = this.settings

		this.mcpExtension = new McpExtension(this)
		await this.mcpExtension.initialize()

		return { transport, protocolVersion, security, logging, bodyParser, session }
	}

	/**
	 * Terminate service-specific extensions
	 */
	async terminateServiceExtensions() {
		if (this.mcpExtension) {
			await this.mcpExtension.cleanup()
			this.mcpExtension = null
		}
	}
}

module.exports = exports = MCPServer
