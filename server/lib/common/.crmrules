You are an expert in Node.js, Typescript and Loopback v3

# Tools
- use yarn, not npm
- editor: use tab size 3, not spaces, no semicolons style

# Service Configurations
1. `server/config`
    - events published and subscribed to are configured in `eventbus.json`
    - enabled providers and subscribed provider events are in `providers.json`
2. `datasources.json`
    - datasources used by the service are configured here
3. `model-config.json` - model configurations
4. `middleware`
    - shared across services: `/server/lib/common/middleware`
    - service specific: `/server/middleware`
5. `component-config.json`
    - loopback components settings

# Service Modules
- found in `server/lib/common/modules`

# Models
- General background: `README.md`:
- Service specific schemas: `/server/models`
- Shared schemas across services (partial definitions):
  - `/server/lib/crm/models`
  - `/server/lib/common/models`

# Mixins
- Extends model behaviors and endpoints:
- Service specific mixins : `/server/mixins`
- For shared models:
  - `/server/lib/crm/mixins`
  - `/server/lib/common/mixins`

# API Endpoints
- API endpoints details: `README.md` & `/docs/API.md`

# Events
1. Use the Event Registry found in `@perkd/event-registry-crm` package for event names
2. Use full event names in the Event Registry, code uses shortened names without domains mostly
3. Subscribed events:
    - service events found in `eventbus.json`
    - provider events found in `providers.json`
4. Exhaustive list of events published to be discovered in codebase
5. Event data structure needs to be disovered in codebase
6. Use the EventBus to publish and subscribe to events

# Testing
1. Use the test suite found in `tests` folder
2. When fixing failed tests:
  - always analyse and understand the test cases thoroughly
  - understand the expected outcomes
  - understand the actual outcomes
  - be able to explain the root cause of the failures
  - reference past fixes, consider reverting previous fixes if they are not needed
  - before fixing them

# Documentation
1. All project documentation is found in the `docs` folder, except for the README.md file
2. Package documentation:
  - all: always check README of the package in node_modules first
  - public: if necessary, additionally check using context7 mcp when available, then check online sources
3. Do not include Service Configuration in the documentation unless explicitly asked
4. Do not include common understanding of the application in the documentation unless asked
5. When creating or reviewing README:
  - Published events:
    - should be configured in eventbus.json (wildcard maybe used) and there should be code that emits the event
  - Subscribed events:
    - should be configured in eventbus.json and there should be registered handler for it
6. Prefer mermaid diagrams over ASCII diagrams, unless latter is more effective
7. Use significantly darker shades of colors for background with white text for better contrast when styling diagrams

# Ignore files
*.env
*.pem
dist/
references/
