/**
 * @module Context Setup
 * Bootstrap script to ensure proper context setup at application startup
 */

const debug = require('debug')('multitenant:setup')

// Use trap mode during startup to allow safe fallbacks
process.env.CONTEXT_MODE = 'trap'

// Print startup banner for context mode
debug('===== CONTEXT MODE: TRAP (STARTUP) =====')
debug('Trap mode enabled during startup')
debug('Will switch to strict mode after bootstrap completes')

// Context is now established in server.js before any other imports
debug('Context setup module loaded - context should already be established')

/**
 * Switch to strict mode after bootstrap completes
 */
function enableStrictMode() {
	process.env.CONTEXT_MODE = 'strict'
	debug('===== CONTEXT MODE: STRICT =====')
	debug('Strict mode enabled to prevent context mixing in parallel requests')
	debug('Hybrid/domain mode is completely disabled')
}

// Export a setup function for application startup
module.exports = function setupContext(app) {
	// Log the context mode at startup
	app.on('started', () => {
		debug('Application started with STRICT context mode')
	})

	// Add a health check route to verify context mode
	app.get('/health/context-mode', (req, res) => {
		res.json({
			contextMode: process.env.CONTEXT_MODE,
			isStrict: process.env.CONTEXT_MODE === 'strict',
			time: new Date().toISOString()
		})
	})

	return app
}

// Export the strict mode enabler
module.exports.enableStrictMode = enableStrictMode