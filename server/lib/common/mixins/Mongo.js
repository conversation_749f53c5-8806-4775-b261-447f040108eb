/**
 *  @module Mixin:Mongo
 */

const { pickObj } = require('@perkd/utils')

const MONGO_OPERATORS = [ '$currentDate', '$inc', '$max', '$min', '$mul', '$rename', '$setOnInsert', '$set', '$unset', '$addToSet', '$pop', '$pullAll', '$pull', '$pushAll', '$push', '$bit' ]

module.exports = function(Model, options) {

	/**
	 * Query mongodb via dataSource.connector (10x faster than remote api)
	 * - https://mongodb.github.io/node-mongodb-native/3.3/api/Collection.html#find
	 * @param  {object} query  mongo-syntax {find:[{}, {options}]}
	 * @param  {object} options {countOnly:true, instance: false}, default false
	 * @return {Object[]|number} list
	 */
	Model.query = async function(query = {}, options = {}) {
		const { countOnly, instance } = options,
			methods = Object.keys(query)

		try {
			let collection = await Model.getDataSource().connector.collection(Model.name)

			for (const method of methods) {
				const params = Array.isArray(query[method]) ? query[method] : [ query[method] ]
				collection = collection[method](...params)
			}

			if (countOnly) return collection.count()

			const list = await collection.toArray()
			return instance ? list.map(buildInstance) : list
		}
		catch (err) {
			appNotify(`${Model.name}.query`, { query, options, err })
		}
	}

	Model.doQuery = async function(filter, options = {}) {
		const query = {},
			{ limit, skip, sort } = filter,
			opt = { limit, skip, sort }

		filter.fields && (opt.projection = filter.fields)
		query.find = [ filter.where, opt ]

		return Model.query(query, options)
	}

	Model.updateMany = async function(filter, updateData) {
		try {
			const collection = await Model.getDataSource().connector.collection(Model.name),
				result = await collection.updateMany(filter, updateData)
			return result.modifiedCount
		}
		catch (err) {
			appNotify(`${Model.name}.updateMany`, { filter, updateData, err })
		}
	}

	Model.collection = function() {
		return Model.getDataSource().connector.collection(Model.name)
	}

	/**
	 * Modifies an existing document in a collection.
	 * @param	{Object} updateData
	 * @param	{Object} opt
	 * 			{Object} hookState
	 * @return	{Promise<Model>} instance
	 */
	Model.prototype.updateDocument = async function(updateData = {}, opt = {}) {
		try {
			const self = this, collection = await Model.getDataSource().connector.collection(Model.name),
				context = {
					Model,
					where: { id: self.id.toString() }, // TODO: byIdQuery, getIdValue,
					data: updateData,
					currentInstance: self,
					isNewInstance: false,
					hookState: opt.hookState || {},
					options: opt,
				},
				// TODO: _sanitizeData
				// TODO: Make sure id(s) cannot be changed
				beforeSave = await Model.notifyObserversOf('before save', context),
				// TODO: applyStrictCheck
				// TODO: inst.setAttributes
				// TODO: inst.isValid
				// TODO: convertSubsetOfPropertiesByType
				// TODO: _sanitizeData
				ctx = await Model.notifyObserversOf('persist', beforeSave)

			const { data, options, where } = ctx

			// TODO: idName
			setMongoId(collection, where)

			// add data to $set
			data.$set = data.$set || {}
			const keys = Object.keys(data)

			for (let i = 0; i < keys.length; i++) {
				const key = keys[i]

				if (MONGO_OPERATORS.includes(key)) continue
				data.$set[key] = data[key]
				delete data[key]
			}
			// TODO: toDatabase

			options.returnOriginal = false

			const updated = await collection.findOneAndUpdate(where, data, options)
			ctx.data = (updated && updated.value) || null
			if (!ctx.data) throw errorIdNotFoundForUpdate(ctx.Model.name, where._id)

			const cntxt = await Model.notifyObserversOf('loaded', ctx),
				contextFinal = {
					...pickObj(cntxt, [ 'Model', 'isNewInstance', 'hookState', 'options' ]),
					instance: buildInstance(cntxt.data),
				},
				afterSave = await Model.notifyObserversOf('after save', contextFinal),
				{ instance } = afterSave
			return instance
		}
		catch (err) {
			appNotify(`${Model.name}.updateDocument`, { id: this.id, updateData, err })
		}
	}

	// -----  Private functions  -----

	function buildInstance(data) {
		const options = {
			applySetters: false,
			persisted: true,
		}
		if (Model.settings.applyDefaultsOnReads === false) {
			options.applyDefaultValues = false
		}

		setModelId(data)
		return new Model(data, options)
	}

	function setMongoId(collection, filter) {
		const idName = Model.definition.idName() || 'id',
			idValue = filter[idName],
			ObjectId = collection.s.pkFactory

		if (idValue) {
			filter._id = ObjectId(idValue)
			if (idName !== '_id') delete filter[idName]
		}
	}

	function setModelId(data) {
		const idName = Model.definition.idName() || 'id',
			idValue = data._id

		if (idValue) {
			data[idName] = idValue.toString()
			if (idName !== '_id') delete data._id
		}
	}

	function errorIdNotFoundForUpdate(modelvalue, idValue) {
		const msg = 'No ' + modelvalue + ' found for id ' + idValue
		const error = new Error(msg)
		error.statusCode = error.status = 404
		return error
	}
}
