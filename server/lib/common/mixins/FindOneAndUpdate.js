/**
 *  @module Mixin:FindOneAndUpdate
 */

const { ObjectId, Mongo } = require('@perkd/utils'),
	{ isMongoId } = Mongo,
	traverse = require('traverse')

module.exports = function(Model) {

	/**
	 * Find a document and update it in one atomic operation.
	 *  - https://mongodb.github.io/node-mongodb-native/4.7/classes/Collection.html#findOneAndUpdate
	 * @param	{Object} filter  The selection criteria for the update
	 * @param	{Object} data	The update document
	 * @param	{Object} options Optional settings (ie. { upsert: false, returnDocument: 'before'|'after' })
	 * @return	{Promise<Model>} instance
	 * Supports 2 hooks:
	 * 		before save // no currentInstance in context
	 * 		after save
	 */

	/*
	Mongodb findOneAndUpdate return value format:
	foundAndUpdated: {"lastErrorObject":{"n":1,"updatedExisting":true},"value":{<Instance>},"ok":1}
	notFound: {"lastErrorObject":{"n":0,"updatedExisting":false},"value":null,"ok":1}
	*/
	Model.findOneAndUpdate = async function(filter, data, options = {}) {
		const collection = Model.getDataSource().connector.collection(Model.name)

		options.returnDocument = 'after'
		setMongoId(filter)
		convertObjectId(data)

		const context = {
			Model: Model.name,
			where: filter,
			data: { ...data },
			isNewInstance: false,
			hookState: options.hookState || {},
			options,
		}
		try {
			await Model.notifyObserversOf('before save', context)

			const res = await collection.findOneAndUpdate(filter, data, options),
				{ value } = res

			if (!value) return null

			const instance = buildInstance(value)

			context.instance = instance
			await Model.notifyObserversOf('after save', context)

			return instance
		}
		catch (err) {
			appNotify(`${Model.name}.findOneAndUpdate`, { filter, data, options, err })
			return null
		}
	}

	// -----  Private functions  -----

	function buildInstance(data) {
		const options = {
			applySetters: false,
			persisted: true,
		}

		if (Model.settings.applyDefaultsOnReads === false) {
			options.applyDefaultValues = false
		}
		setModelId(data)
		return new Model(data, options)
	}

	function setMongoId(filter) {
		const idName = Model.definition.idName() || 'id',
			idValue = filter[idName]

		if (idValue) {
			filter._id = ObjectId(idValue)
			if (idName !== '_id') delete filter[idName]
		}
	}

	function setModelId(data) {
		const idName = Model.definition.idName() || 'id',
			idValue = data._id

		if (idValue) {
			data[idName] = idValue.toString()
			if (idName !== '_id') delete data._id
		}
	}

	function convertObjectId(data) {
		const { $set: update } = data
		if (!update) return
		data.$set = traverse(update).forEach(convert)

		function convert (o) {
			return (typeof o === 'string' && isMongoId(o)) ? ObjectId(o) : o
		}
	}

	// -----  Remote methods  -----

	Model.remoteMethod('findOneAndUpdate', {
		description: `Find first ${Model.name} instance of the model matched by filter and update it in one atomic operation.`,
		http: { path: '/findOneAndUpdate', verb: 'post' },
		accepts: [
			{ arg: 'filter', type: 'object' },
			{ arg: 'data', type: 'object' },
			{ arg: 'options', type: 'object' },
		],
		returns: { type: `${Model.name}`, root: true },
	})
}
