/**
 *  @module Mixin:MultitenantRemote   (Remote model multi-tenancy)
 */

const { EventEmitter } = require('node:events'),
	{ Apis } = require('@crm/types'),
	{ Context } = require('@perkd/multitenant-context'),
	{ cloneDeep, Security } = require('@perkd/utils')

const { Jwt } = Security,
	{ Headers } = Apis,
	{ X_ACCESS_TOKEN } = Headers

module.exports = function(Model) {
	const { remotes } =  Model.dataSource?.connector || {}

	if (remotes) {
		injectToken(remotes, Model.modelName, Model)
	}
	else {
		Model.on('dataSourceAttached', model => {
			const ds = model.getDataSource(),
				{ connector } = ds ?? {},
				{ remotes } = connector ?? {}

			if (remotes) {
				injectToken(remotes, Model.modelName, Model)
			}
		})
	}
}

function injectToken(remotes, modelName, Model) {
	remotes.before('**', async (ctx, next, method) => {
		// Use context utilities to ensure proper context isolation
		await runWithContext(async () => {
			const { accessToken } = Context,
				{ secretKey } = Model.app?.service || {}
			// Check if we might be using the wrong context
			// const existingToken = ctx.req?.headers?.[X_ACCESS_TOKEN]

			ctx.req.headers = ctx.req.headers || {}
			ctx.req.headers[X_ACCESS_TOKEN] = (accessToken && validateToken(accessToken, secretKey)) ? accessToken : Context.generateAccessToken()
		}, { operation: `${modelName}.${method.name}` })
	})
}

/**
 * Runs an async function with the current context preserved
 * This ensures the tenant context isn't lost during async operations
 *
 * @param {Function} fn Async function to run with preserved context
 * @param {Object} options Options for the operation
 * @param {String} options.operation Name of operation being performed (for logging)
 * @returns {Promise} Result of the function
 */
async function runWithContext(fn, { operation = 'unknown' } = {}) {
	const currentContext = Context.getCurrentContext()
	if (!currentContext) {
		debug(`No context available for operation: ${operation}`)
		return fn() // Just run the function normally if no context exists
	}

	const beforeTenant = Context.tenant
	let result
	try {
		// Clone the context but handle circular references
		const contextToUse = { ...currentContext }

		// Remove _domain to break circular reference before cloning
		if (contextToUse._domain) {
			delete contextToUse._domain
		}

		result = await Context.runInContext(cloneDeep(contextToUse), fn)
	}
	catch (error) {
		throw error
	}
	finally {
		// Verify context wasn't changed
		const afterTenant = Context.tenant
		if (beforeTenant !== afterTenant) {
			debug(`WARNING: Context changed after ${operation}, before: ${beforeTenant || 'none'}, after: ${afterTenant || 'none'}`)
		}
	}

	return result
}

function validateToken(accessToken, secretKey) {
	if (!accessToken) return true
	const jwt = new Jwt(secretKey)
	let decodedJWT

	if (jwt.verify(accessToken)) {
		decodedJWT = jwt.decode(accessToken)
	}
	else return false

	const payload = (typeof decodedJWT.payload === 'string')
			? JSON.parse(decodedJWT.payload)
			: decodedJWT.payload,
		{ exp } = payload,
		now = Math.floor(Date.now() / 1000)
	if (exp && exp < now) console.log('[debug] Token expired', accessToken)
	return !exp || exp > now
}