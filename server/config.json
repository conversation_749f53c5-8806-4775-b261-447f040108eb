{"restApiRoot": "/api", "host": "0.0.0.0", "port": 8140, "remoting": {"cors": false, "rest": {"normalizeHttpPath": false, "handleErrors": false, "xml": false}, "json": {"strict": false, "limit": "10mb"}, "urlencoded": {"extended": true, "limit": "2mb"}}, "service": {"name": "Sync", "domain": "sync", "version": "1.0.0", "description": "", "appPath": "lib/", "settings": ["settings"], "dependencies": {}, "autoStart": true, "canTerminate": true, "state": {"now": 0, "text": "", "since": ""}, "multitenancy": false, "tenantCode": "perkd"}, "modules": {"metrics": {"enabled": true}, "eventbus": {"enabled": true}, "sync": {"enabled": true}, "notify": {"channel": "-sync"}, "watchdog": {"enabled": true, "cloudWatch": {"awsRegion": "us-west-2"}}}, "apiRequest": {"version": "1.0.0", "apiVersion": 0, "timeout": 60000, "https": false, "host": "", "port": "", "apiRoot": "/api", "multitenancy": false}}