const LOCALHOST = '127.0.0.1',
	{
		NODE_ENV,
		// DB_HOST = NODE_ENV ? 'mongodb' : LOCALHOST,
		// DB_USERNAME,
		// DB_PASSWORD,
		DB_HOST = '*************',
		DB_USERNAME = '',
		DB_PASSWORD = '',
		DB_AUTH = 'admin',
		DB_SET = '',
		SERVICE_HOST = LOCALHOST,
		ACCOUNT_HOST = SERVICE_HOST,
		PERSON_HOST = SERVICE_HOST,
		CARD_HOST = SERVICE_HOST,
		INSTALLATION_HOST = SERVICE_HOST,
		OFFER_HOST = SERVICE_HOST,
		REWARD_HOST = SERVICE_HOST,
		RICH_MESSAGE_HOST = SERVICE_HOST,
		PLACE_HOST = SERVICE_HOST,
		WIDGET_HOST = SERVICE_HOST,
		ACTION_HOST = SERVICE_HOST
	} = process.env

module.exports = {
	default: {
		name: 'default',
		connector: 'mongodb',
		url: 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${encodeURIComponent(DB_PASSWORD)}@` : '') + `${DB_HOST}/perkd?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}&readPreference=primaryPreferred&slaveOk=true` : '') + '&useNewUrlParser=true&useUnifiedTopology=true&maxPoolSize=50&socketTimeoutMS=360000&connectTimeoutMS=10000&maxIdleTimeMS=300000',
		allowExtendedOperators: true,
		enableOptimisedfindOrCreate: true,
	},
	accountRemote: {
		name: 'accountRemote',
		connector: 'remote',
		// url: `http://${ACCOUNT_HOST}:8150/api`,
		url: 'https://account.test.perkd.perkd.me/api',
	},
	personRemote: {
		name: 'personRemote',
		connector: 'remote',
		// url: `http://${PERSON_HOST}:8101/api`,
		url: 'https://person.test.perkd.perkd.me/api',
	},
	cardRemote: {
		name: 'cardRemote',
		connector: 'remote',
		// url: `http://${CARD_HOST}:8117/api`,
		url: 'https://card.test.perkd.perkd.me/api',
	},
	installationRemote: {
		name: 'installationRemote',
		connector: 'remote',
		// url: `http://${INSTALLATION_HOST}:8104/api`,
		url: 'https://installation.test.perkd.perkd.me/api',
	},
	offerRemote: {
		name: 'offerRemote',
		connector: 'remote',
		// url: `http://${OFFER_HOST}:8119/api`,
		url: 'https://offer.test.perkd.perkd.me/api',
	},
	rewardRemote: {
		name: 'rewardRemote',
		connector: 'remote',
		// url: `http://${REWARD_HOST}:8121/api`,
		url: 'https://reward.test.perkd.perkd.me/api',
	},
	richMessageRemote: {
		name: 'richMessageRemote',
		connector: 'remote',
		// url: `http://${RICH_MESSAGE_HOST}:8111/api`,
		url: 'https://rich-message.test.perkd.perkd.me/api',
	},
	placeRemote: {
		name: 'placeRemote',
		connector: 'remote',
		// url: `http://${PLACE_HOST}:8115/api`,
		url: 'https://place.test.perkd.perkd.me/api',
	},
	widgetRemote: {
		name: 'widgetRemote',
		connector: 'remote',
		// url: `http://${WIDGET_HOST}:8123/api`,
		url: 'https://widget.test.perkd.perkd.me/api',
	},
	actionRemote: {
		name: 'actionRemote',
		connector: 'rest',
		options: {
			headers: {
				accepts: 'application/json',
				'content-type': 'application/json',
			},
		},
		operations: [
			{
				functions: {
					syncFetchRest: [ 'personId', 'last', 'token' ],
				},
				template: {
					method: 'GET',
					url: 'https://action.perkd.me/test/Actions/sync/fetch?personId={personId}&last={last}',
					headers: {
						'x-access-token': '{token}',
					},
					responsePath: '$'
				},
			}
		],
	}
}
