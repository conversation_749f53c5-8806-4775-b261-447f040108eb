# Sync Service
A microservice for handling two-way data synchronization between Perkd App and Perkd Platform services.

## Table of Contents
- [Overview](#overview)
- [Architecture](#architecture)
- [Core Features](#core-features)
- [Sync Process Flow](#sync-process-flow)
- [Conflict Resolution](#conflict-resolution)
- [Sync Engine](#sync-engine)
- [Service Integration](#service-integration)
- [Perkd App Integration](#perkd-app-integration)
- [Data Models](#data-models)
- [API Endpoints](#api-endpoints)
- [Events](#events)

## Overview
This service is a critical component of the Perkd platform that enables seamless data synchronization between Perkd App and backend services. It handles both client-initiated changes and server updates while managing conflicts and ensuring data consistency across multiple devices.

### System Architecture
```mermaid
graph TD
    %% Top Level Services
    PerkdApp["Perkd App"]
    BackendServices["Backend Services"]
    EventBus["Event Bus"]

    %% Sync Service Layer
    subgraph SyncService["Sync Service"]
        direction TB

        subgraph APILayer["API Layer"]
            BiSync["Sync API"]
            TargetFetch["Fetch API"]
            ModelSync["Model Sync"]
        end

        subgraph SyncCore["Sync Core"]
            SyncManager["Sync Manager"]
            VersionMgr["Version<br/>Manager"]
            ConflictRes["Conflict<br/>Resolution"]
            DataTransform["Data<br/>Transformer"]
            ModelHandler["Model Sync<br/>Handler"]
        end

        subgraph Storage["Storage Layer"]
            ChangeStore["Change Store<br/>(User/Device Data)"]
            Changes["Change Tracking<br/>(Event History)"]
            Delta["Delta Storage<br/>(JSONPatch)"]
        end
    end

    %% Connections
    PerkdApp -->|"objectsUp[]"| BiSync
    PerkdApp -->|"objectIds[]"| TargetFetch
    BackendServices -->|"model.*.created/updated/deleted"| EventBus
    EventBus --> ModelSync

    BiSync & TargetFetch & ModelSync --> SyncManager

    SyncManager --> VersionMgr
    SyncManager --> ConflictRes
    SyncManager --> DataTransform
    SyncManager --> ModelHandler

    ModelHandler <--> ChangeStore
    ModelHandler <--> Changes
    ModelHandler --> EventBus
    ConflictRes --> Delta

    ModelHandler <-->|"Model updates"| BackendServices

    %% Return paths
    BiSync -->|"objectsDown[]"| PerkdApp
    TargetFetch -->|"objects[]"| PerkdApp

    %% Styling
    classDef api fill:#6c8ebf,stroke:#6c8ebf,color:white
    classDef app fill:#d79b00,stroke:#d79b00,color:white
    classDef core fill:#82b366,stroke:#82b366,color:white
    classDef storage fill:#9673a6,stroke:#9673a6,color:white
    classDef backend fill:#b85450,stroke:#b85450,color:white

    class PerkdApp app
    class BiSync,TargetFetch,ModelSync api
    class SyncManager,VersionMgr,ConflictRes,DataTransform,ModelHandler core
    class ChangeStore,Changes,Delta storage
    class BackendServices backend
```

1. **API Layer**: Handles client requests and responses
   - REST endpoints for sync operations
   - Request validation and rate limiting (60 requests/minute for sync endpoints)
   - Response compression and formatting (max 10MB payload)
   - Authentication and authorization with JWT tokens

2. **Sync Core**: Manages sync logic and conflict resolution
   - Change detection and tracking with version-based conflict detection
   - Version management using incremental version numbers
   - Conflict resolution using server-wins strategy with JSONPatch
   - Data transformation and validation with schema versioning

3. **Storage Layer**: Redis-based storage for sync state and changes
   - Change Store: Stores sync data per user/device
   - Change Tracking: Records model events with timestamps
   - Delta Storage: Manages JSONPatch deltas for conflict resolution
   - Multi-tenant data isolation
   - Configurable instance limits (20,000 per store)

4. **Backend Integration**: Connects with various backend services


## Core Features
```mermaid
graph LR
    subgraph ServerSide[Server-side Updates]
        Backend[Backend Services]
        Bus[Event Bus]
        Store[Change Store]
    end

    subgraph AppSide[App-initiated Sync]
        App[Perkd App]
        Sync[Sync Service]
    end

    Backend -->|"1.emit changes"| Bus
    Bus -->|"2.track"| Store
    App -->|"3.sync request<br/>objectsUp[]"| Sync
    Store -->|"4.check state"| Sync
    Sync -->|"5.update"| Backend
    Sync -->|"6.emit events"| Bus
    Sync -.->|"7.return<br/>objectsDown[]"| App

    %% Styling
    classDef cache fill:#9673a6,stroke:#9673a6,color:white
    classDef sync fill:#2D5B2D,stroke:#2D5B2D,color:white

    class Store cache
    class Sync sync
```

1. **Bi-directional Synchronization**
   - Handles data flow between Perkd App and backend services
   - Supports offline data storage and sync with state tracking (ONLINE/OFFLINE/SUSPEND)
   - Manages incremental updates using timestamps and version tracking
   - Ensures data consistency across multiple devices
   - Provides real-time updates through event-driven architecture

2. **Robust Conflict Resolution**
   - Implements version-based conflict detection with JSONPatch comparison
   - Applies *server-wins* strategy with delta tracking for automatic resolution
   - Provides conflict details to clients for manual resolution
   - Maintains audit trail through timestamp tracking (createdAt, modifiedAt)
   - Returns conflicting changes to Perkd App with detailed delta information
   - See [Conflict Resolution](#conflict-resolution) for details

3. **Change Store & Tracking**
   - Implements distributed change tracking using Redis as backend storage
   - Configurable instance limits (default: 20,000 instances per store)
   - Event-driven change tracking with automatic invalidation
   - Tenant-aware architecture with data isolation
   - Modular Cache mixin for flexible behavior
   - Automatic event tracking for created/updated/deleted operations
   - Fallback mechanisms with error tracking and recovery
   - Change tracking with optimized delta updates
   - Implementation details:
     - Uses Redis for distributed storage and pub/sub
     - Tracks changes with timestamps and JSONPatch deltas
     - Supports multi-device sync through personId/deviceId tracking
     - Handles cache invalidation through event bus integration
     - Provides automatic cleanup through configurable purge times

4. **Performance & Scalability**
   - Handles high concurrent sync requests with configurable rate limits (60 requests/minute for sync)
   - Implements batch processing for efficient sync operations
   - Supports delta updates to minimize payload size using JSONPatch
   - Provides compression for large data transfers (configured up to 10MB)
   - Implements basic retry mechanisms for service recovery
   - Provides comprehensive error tracking and automatic recovery procedures
   - Supports request idempotency for reliable operations
   - Implements streaming responses for large datasets:
   ```javascript
   // Create a stream for the response
   const stream = sync.streams.create(res, install);

   // Begin the stream with appropriate headers
   stream.begin(head);

   // Stream objects as they become available
   stream.objects(objects);

   // End the stream
   stream.end(tail, syncUntil);
   ```

## Sync Process Flow

The sync process consists of two main flows:

1. **Global Sync via Sync Service**: Synchronizes data across multiple models through the Sync Service
2. **Model-specific Sync via Backend Services**: Synchronizes data for specific models through their respective endpoints

### Flow 1: Global Sync via Sync Service

```mermaid
graph TB
    %% Backend Services Layer
    BackendServices["Backend Services"]

    %% Event Bus Layer
    EventBus["Event Bus"]

    %% Storage Layer
    subgraph Storage["Change Store"]
        Changes["Change Tracking"]
        Delta["Delta Storage"]
        State["Current State"]
    end

    %% Sync Service Layer
    subgraph Sync["Sync Service"]
        API["API Layer"]
        Core["Sync Core"]
        Conflict["Conflict Resolution"]
    end

    %% Mobile App Layer
    subgraph App["Perkd App"]
        Local[(Local Storage)]
        Manager["Sync Manager"]
    end

    %% Part 1: Server-side Updates
    BackendServices -->|emit| EventBus
    EventBus -->|track| Changes
    Changes -->|update| State
    Delta -->|store| State

    %% Part 2: App-initiated Sync
    Manager -->|1.POST /Cache/app/sync| API
    Manager -->|1.POST /:model/app/sync| API
    API -->|2.process| Core
    Core -->|3.check| Changes
    Core -->|4.resolve| Conflict
    Conflict -->|5.apply| Core
    Core -->|6.update| BackendServices
    Core -->|7.response| Manager
    Manager -->|8.persist| Local

    %% Styling
    classDef storage fill:#9673a6,stroke:#9673a6,color:white
    classDef sync fill:#2D5B2D,stroke:#2D5B2D,color:white
    classDef app fill:#d79B00,stroke:#F9AD14,color:white

    class Changes,Delta,State storage
    class API,Core,Conflict sync
    class Local,Manager app
```

### Flow 2: Model-specific Sync via Backend Services

```mermaid
graph TB
    %% Mobile App Layer
    subgraph App["Perkd App"]
        Local[(Local Storage)]
        Manager["Sync Manager"]
    end

    %% Backend Services Layer
    subgraph BackendServices["Backend Services"]
        subgraph BackendAPILayer["API Layer"]
            ModelSync["/:model/app/sync"]
            ModelFetch["/:model/app/fetch"]
        end
        ModelSyncFetch["syncFetch()"]
        ModelSyncUpdate["syncUpdate()
        (optional)"]
        SyncMixin["Sync Mixin"]
        CacheMixin["Cache Mixin"]
    end

    %% Storage Layer
    subgraph Storage["Change Store"]
        Changes["Change Tracking"]
        State["Current State"]
    end

    %% App-initiated Model-specific Sync
    Manager -->|1.POST /:model/app/sync| ModelSync
    Manager -->|2.GET /:model/app/fetch| ModelFetch

    %% Backend Services Components
    SyncMixin -->|provides| ModelSync
    SyncMixin -->|provides| ModelFetch
    SyncMixin -->|calls if available| ModelSyncUpdate
    SyncMixin -->|calls| ModelSyncFetch
    SyncMixin -->|depends on| CacheMixin
    CacheMixin -->|requires| ModelSyncFetch

    %% Model-specific Sync Flow
    ModelSync -->|check| Changes
    ModelSync -->|update| State
    ModelFetch -->|retrieve from| State
    ModelSync -->|response| Manager
    ModelFetch -->|response| Manager
    Manager -->|persist| Local

    %% Styling
    classDef storage fill:#9673a6,stroke:#9673a6,color:white
    classDef sync fill:#2D5B2D,stroke:#2D5B2D,color:white
    classDef backend fill:#6c8ebf,stroke:#6c8ebf,color:white
    classDef app fill:#d79b00,stroke:#F9AD14,color:white

    class Changes,State storage
    class SyncMixin sync
    class Local,Manager app
    class ModelSyncFetch,ModelSyncUpdate,CacheMixin,ModelSync,ModelFetch backend
```

## Sync Process Details

The sync process includes the following key components:
   - Backend services emit model events (created/updated/deleted)
   - Event Bus validates and routes events to ChangeStore
   - ChangeStore records changes with version numbers and timestamps
   - JSONPatch deltas are generated for conflict resolution
   - Changes are indexed for efficient retrieval

```mermaid
sequenceDiagram
    participant Backend as Backend Services
    participant Bus as Event Bus
    participant Store as Change Store

    Backend->>Bus: emit data changes
    Note right of Backend: created/updated/deleted

    Bus->>Store: track changes
    Note right of Bus: with timestamps

    Store->>Store: store changes
    Note right of Store: for future sync
```

2. **App-initiated Sync (Request-driven)**
   - Perkd App collects local changes with version metadata
   - Changes are bundled into `objectsUp` array (max 500 objects)
   - App can sync through two pathways:
     - **Global Sync**: Via `/Cache/app/sync` endpoint in the Sync Service
     - **Model-specific Sync**: Via `/:model/app/sync` endpoints exposed by Backend Services
   - Sync Service validates incoming changes
   - Service checks ChangeStore for server-side updates
   - Version conflicts are detected and resolved
   - Backend services are updated with resolved changes using server-wins strategy
   - ChangeStore is updated with new versions
   - App receives changes `objectsDown` with server changes and updates local storage

3. **Model-specific Sync Implementation**
   - Model-specific endpoints (`/:model/app/sync` and `/:model/app/fetch`) are exposed by Backend Services
   - These endpoints are added to models through the Sync mixin provided by the Sync Service
   - The actual implementation is a collaboration:
     - Backend Services implement required `syncFetch` method for data retrieval
     - Backend Services optionally implement `syncUpdate` for model-specific update logic
     - Sync Service provides the core sync functionality, conflict resolution, and change tracking
   - When a model-specific sync endpoint is called:
     1. The model's API receives the request
     2. It calls the model's `syncUpdate` method if available (for custom logic)
     3. Then it delegates to the Sync Service's core functionality
     4. The Sync Service handles the actual sync process, conflict resolution, etc.

```mermaid
sequenceDiagram
    participant App as Perkd App
    participant Sync as Sync Service
    participant Store as Change Store
    participant Backend as Backend Services
    participant Bus as Event Bus

    App->>Sync: POST /Cache/app/sync with objectsUp[]
    Note right of App: local changes

    Sync->>Store: check state
    Note right of Sync: conflict detection

    alt Has Conflicts
        Sync->>Sync: resolve conflicts
        Note right of Sync: server-wins strategy
    end

    Sync->>Backend: update if needed
    Backend-->>Sync: confirm updates

    Sync->>Bus: emit sync events
    Note right of Sync: sync.completed/failed

    Sync-->>App: return objectsDown[]
    Note right of App: with delta updates
```

### Sync States
- **ONLINE**: Normal bi-directional sync
- **OFFLINE**: Local-only operations with queued changes
- **SUSPEND**: Temporary pause in sync operations
- **ERROR**: Failed sync with retry mechanism

State transitions are managed through app events:
```javascript
function setOffline(installation, at) {
  const install = { ...installation };
  install.state = OFFLINE;
  return refresh(install, at);
}

function setSuspend(installation, at) {
  const install = { ...installation };
  install.state = SUSPEND;
  return refresh(install, at);
}

function setOnline(installation, at) {
  const install = { ...installation };
  install.state = ONLINE;
  return refresh(install, at);
}

function refresh(install, at = new Date()) {
  install.stateSince = at;
  install.lastSeenAt = at;
  return install;
}
```

### Version Management
- Each model instance has an incremental version number
- Version conflicts trigger JSONPatch comparison
- Server version always wins in conflicts
- Client receives conflict details for manual resolution
- Version history is maintained for 30 days

### Process Steps
1. **Client Initialization**
	- App prepares local changes
	- Collects sync metadata (timestamps, limits)
	- Bundles changes into `objectsUp` array

2. **Server Processing**
	- Validates incoming changes
	- Applies changes to backend services
	- Detects and resolves conflicts
	- Prepares server-side updates

3. **Response Handling**
	- Server returns `objectsDown` with updates
	- Updates sync timestamp (`syncUntil`)
	- Client applies changes to local storage
	- Updates sync state for next iteration

## Sync Types

1. **Bi-directional Sync** (`POST /Cache/app/sync`)
	- Client sends local changes in `objectsUp`
	- Server processes changes with conflict resolution
	- Server returns updated data within specified limits
	- Supports background sync mode with `background` flag
	- Background sync processes updates asynchronously
	- Returns partial results for background operations
	- Handles legacy app versions (pre-7.0) with automatic model name conversion:
	```javascript
	const legacy = isPre7app(installation);
	if (legacy) { // compat for pre 7.0, convert Preference to AccountSetting
	  const index = objectsUp.findIndex(o => o._model === 'Preference');
	  if (index !== -1) objectsUp[index]._model = 'AccountSetting';
	}
	```

2. **Targeted Fetch** (`GET /:model/app/fetch`)
	- Client requests specific objects by ID
	- Server returns requested objects with current state
	- Optimized for minimal data transfer

3. **Model-Specific Sync**
	- Each backend service model exposes its own sync endpoints (`POST /:model/app/sync` and `GET /:model/app/fetch`)
	- These endpoints are added to models through the Sync mixin provided by the Sync Service
	- The implementation is a collaboration between Backend Services and the Sync Service:
	  - Backend Services implement the required `syncFetch` method for data retrieval
	  - Backend Services optionally implement `syncUpdate` for model-specific update logic
	  - Sync Service provides the core sync functionality, conflict resolution, and change tracking
	- Models implementing sync: Action, AppEvent, Card, Offer, Reward, Message, Person, Place, AccountSetting, WidgetData
	- Example of a model's `syncFetch` implementation:
	```javascript
	// Card model implementation
	Card.syncFetch = async function(personId, last) {
	  const filter = {
	    where: {
	      personId: personId,
	      deletedAt: null
	    }
	  };

	  // Add timestamp filter if provided
	  if (last) {
	    filter.where.or = [
	      { createdAt: { gt: last } },
	      { modifiedAt: { gt: last } }
	    ];
	  }

	  return Card.find(filter);
	}
	```

	- Example of how a model-specific sync endpoint uses the Sync Service:
	```javascript
	Model.sync = async function(objectsUp = [], syncUntil, limit, background, options = {}, req, res) {
	  const { name, syncUpdate } = Model,
	    sync = appModule(SYNC).get(name),
	    after = syncUntil || options.syncUntil || 0,
	    max = limit || options.fetchLimit,
	    { installation } = Context

	  // Call model-specific update logic if available
	  if (objectsUp.length && syncUpdate) {
	    await syncUpdate(objectsUp, installation)
	  }

	  // Delegate to Sync Service for core functionality
	  const result = await sync.fetch(after, max, { ...options })

	  sync.response(result, options.install, res)
	}
	```


## Conflict Resolution
```mermaid
sequenceDiagram
	participant App as Perkd App
	participant API as Sync API
	participant Core as Sync Core
	participant Store as Change Store
	participant Backend as Backend Services

	App->>API: POST /Cache/app/sync with objectsUp[]
	Note over App,API: Local changes with version V1

	API->>Core: Process changes
	Core->>Store: Check current version
	Store-->>Core: Server has V2

	alt Version Conflict (V1 ≠ V2)
		Core->>Core: Generate JSONPatch delta
		Note over Core: Compare V1 vs V2
		Core->>Core: Apply server-wins strategy
		Note over Core: Merge changes prioritizing V2

		Core->>Backend: Update with merged changes
		Backend-->>Core: Confirm update (V3)

		Core->>Store: Store new version V3
		Core-->>API: Return objectsDown[]
		Note over Core,API: Include conflict details
		API-->>App: Return merged state (V3)
		Note over App: Client resolves UI state
	else No Conflict (V1 = V2)
		Core->>Backend: Apply changes directly
		Backend-->>Core: Confirm update
		Core->>Store: Update version
		Core-->>App: Return success
	end
```

The conflict resolution process follows these steps:

1. **Version Check**
	- Client sends changes with their current version (V1)
	- Server checks against latest version in store (V2)
	- Conflict detected if versions don't match

2. **Delta Generation**
	- Server generates JSONPatch delta between versions using deep-diff
	- Compares client version (V1) with server version (V2)
	- Identifies specific field-level changes
	- Handles array changes with unique property tracking
	- Supports nested object comparison
	- Implementation uses the following approach:
	```javascript
	// Generate delta using deep-diff
	const delta = diff(before, after, (path, key) =>
		listPropertyNames.includes(key) || (key.indexOf('List') !== -1 && key.charAt(0) !== '_')
	);

	// For array properties, use specialized diffArray function
	listPropertyNames.forEach((listPropertyName, i) => {
		const modelDelta = getArrayDelta(before, after, uniqueProperties[i], listPropertyName);
		delta = (modelDelta && delta) ? delta.concat(modelDelta) : [modelDelta];
	});
	```

3. **Resolution Strategy**
	- Applies server-wins strategy by default
	- For created objects:
		- Accepts client creation if no server version exists
		- Excludes from conflict resolution
	- For modified objects:
		- If only client changes exist, applies client updates
		- If both client and server changes exist:
			- Generates server and app update sets
			- Applies server changes to current state
			- Returns conflicting app changes to client
	- Preserves automatic timestamps (createdAt, modifiedAt)
	- Handles model-specific second-stage updates
	- Implementation example:
	```javascript
	if (!serverChanges?.length) {  // only app changes
		Model.upsert(update).catch(error => console.log('[syncUpdate]upsert', { error, personId, update }))
		exclude.push(id)
	}
	else {  // both server & app changes
		const current = await Model.findById(id)
		if (current) {
			const { server, app } = updatesFromChanges(current, serverChanges, update)
			// createdAt & modifiedAt should be set automatically by Timestamp mixin
			delete server.createdAt
			delete server.modifiedAt
			await current.updateAttributes(server)
			updated.push(app)
		}
	}
	```

4. **Client Update**
	- Returns merged state to client
	- Includes conflict details in response
	- Client updates local storage to V3
	- Updates UI to reflect resolved state
	- Provides delta information for manual resolution

5. **Change Tracking**
	- Records all changes with timestamps
	- Maintains version history
	- Tracks changes per device and user
	- Supports multi-device synchronization
	- Enables conflict detection across devices

6. **Error Handling**
	- Logs sync failures with detailed context
	- Provides error recovery mechanisms
	- Maintains data consistency during failures
	- Supports retry mechanisms for failed operations
	- Returns clear error messages to client
	- Implementation example:
	```javascript
	try {
	  const result = await sync.fetch(after, max, { ...options });
	  sync.response(result, options.install, res);
	} catch (error) {
	  console.error('[Sync] fetch error:', error);
	  res.status(500).send({ error: 'Sync operation failed' });
	}
	```
	- Event error handling:
	```javascript
	appOn(Event.object.created, async evt => {
	  try {
	    const { sync } = appModule(SYNC),
	      { id, personId = id, createdAt, purgeTime } = evt;
	    await sync.changes.objectCreated(personId, model, String(id), createdAt, purgeTime);
	  }
	  catch (err) {
	    console.error('[Cache] object.created event:', { err, evt });
	  }
	});
	```


## Sync Engine
The Sync Engine provides a modular architecture for backend services to integrate with the Sync service. It enables backend services to implement standardized sync capabilities through a set of mixins and patterns.

### Core Components

1. **syncFetch Method Pattern**
   - Required method that models must implement
   - Standardizes data fetch operations with timestamp-based filtering
   - Example implementation:
   ```javascript
   Model.syncFetch = async function(personId, last) {
     const filter = {
       where: {
         personId: personId,
         deletedAt: null
       }
     };

     // Add timestamp filter if provided
     if (last) {
       filter.where.or = [
         { createdAt: { gt: last } },
         { modifiedAt: { gt: last } }
       ];
     }

     return Model.find(filter);
   }
   ```

2. **Sync Mixin**
   - Handles event-driven cache updates
   - Provides sync API endpoints (`/:model/app/sync` and `/:model/app/fetch`)
   - Processes sync requests and responses

3. **Cache Mixin**
   - Manages Redis-based caching
   - Requires `syncFetch` method implementation
   - Handles CRUD event tracking

4. **Integration Process**
   ```javascript
   // 1. Add required mixins in correct order
   Model.mixin('Sync', {})
   Model.mixin('Cache', {})

   // 2. Implement syncFetch method
   Model.syncFetch = async function(personId, timestamp, ids) {
     // Implementation
   }

   // 3. Emit sync events
   Model.emit(`${service}.${model}.created`, {
     personId,
     timestamp,
     // ... other metadata
   })
   ```

See [Sync Engine](docs/sync-engine.md) for complete implementation details.


## Service Integration
```mermaid
graph TB
    EventBus["Event Bus"]

    subgraph SyncService["Sync Service"]
        subgraph APILayer["API Layer"]
            ModelSync["Model Sync"]
        end

        subgraph SyncCore["Sync Core"]
            SyncManager["Sync Manager"]
            VersionMgr["Version Manager"]
            ConflictRes["Conflict Resolution"]
            DataTransform["Data Transformer"]
            ModelHandler["Model Sync Handler"]
        end

        subgraph Storage["Storage Layer"]
            ChangeStore["Change Store"]
            Changes["Change Tracking"]
            Delta["Delta Storage"]
        end
    end

    subgraph CoreServices["Core Services"]
        Account["Account Service"]
        AccountEngine["Sync Engine"]
        Person["Person Service"]
        PersonEngine["Sync Engine"]
        Installation["Installation Service"]
        InstallEngine["Sync Engine"]
    end

    subgraph BusinessServices["Business Services"]
        Card["Card Service"]
        CardEngine["Sync Engine"]
        CardMaster["Card Master Service"]
        MasterEngine["Sync Engine"]
        Offer["Offer Service"]
        OfferEngine["Sync Engine"]
        Reward["Reward Service"]
        RewardEngine["Sync Engine"]
    end

    subgraph ContentServices["Content Services"]
        RichMessage["Rich Message Service"]
        MessageEngine["Sync Engine"]
        Place["Place Service"]
        PlaceEngine["Sync Engine"]
        Widget["Widget Service"]
        WidgetEngine["Sync Engine"]
        Image["Image Service"]
        ImageEngine["Sync Engine"]
        Action["Action Service"]
        ActionEngine["Sync Engine"]
    end

    %% Sync Service internal connections
    ModelSync --> SyncManager
    SyncManager --> VersionMgr & ConflictRes & DataTransform & ModelHandler
    ModelHandler --> ChangeStore & Changes
    ConflictRes --> Delta

    %% Event Bus connections
    ModelHandler <--> EventBus
    Changes <--> EventBus

    %% Engine connections to Model Sync
    AccountEngine & PersonEngine & InstallEngine --> ModelSync
    CardEngine & MasterEngine & OfferEngine & RewardEngine --> ModelSync
    MessageEngine & PlaceEngine & WidgetEngine & ImageEngine & ActionEngine --> ModelSync

    %% Service-Engine connections
    Account --- AccountEngine
    Person --- PersonEngine
    Installation --- InstallEngine

    Card --- CardEngine
    CardMaster --- MasterEngine
    Offer --- OfferEngine
    Reward --- RewardEngine

    RichMessage --- MessageEngine
    Place --- PlaceEngine
    Widget --- WidgetEngine
    Image --- ImageEngine
    Action --- ActionEngine

    %% Styling
    classDef sync fill:#2D5B2D,stroke:#2D5B2D,color:white
    classDef core fill:#6c8ebf,stroke:#6c8ebf,color:white
    classDef business fill:#d79b00,stroke:#d79b00,color:white
    classDef content fill:#b85450,stroke:#b85450,color:white
    classDef engine fill:#82b366,stroke:#82b366,color:white
    classDef storage fill:#9673a6,stroke:#9673a6,color:white

    class ModelSync,SyncManager,VersionMgr,ConflictRes,DataTransform,ModelHandler sync
    class Account,Person,Installation core
    class Card,CardMaster,Offer,Reward business
    class RichMessage,Place,Widget,Image,Action content
    class AccountEngine,PersonEngine,InstallEngine,CardEngine,MasterEngine,OfferEngine,RewardEngine,MessageEngine,PlaceEngine,WidgetEngine,ImageEngine,ActionEngine engine
    class ChangeStore,Changes,Delta storage
```

- Connects with multiple backend services:
  - Account Service (user accounts, account settings)
  - Person Service (user profiles, preferences)
  - Card Service (loyalty cards, points)
  - Card Master Service (card templates, configurations)
  - Installation Service (device management)
  - Offer Service (promotions, deals)
  - Reward Service (user rewards, redemptions)
  - Rich Message Service (notifications, communications)
  - Place Service (location management)
  - Widget Service (UI components, data)
  - Image Service (image management)
  - Action Service (user actions, tracking)

Each service is configured with:
- Remote connector integration
- Dedicated port allocation
- API endpoint configuration
- Event-driven communication
- Multi-tenant support where applicable

## Perkd App Integration
- Supports iOS and Android devices
- Version-aware data transformations with minimum app version requirements
- Installation tracking with platform and version management
- Background sync support

1. **Downstream (To Perkd App)**
	- Core business objects (actions, cards, offers, rewards, places)
	- Notifications and UI components
	- Device-specific configurations
	- Version-aware data transformations

2. **Upstream (From Perkd App)**
	- User generated content with timestamps
	- Device-specific changes and states
	- Sync metadata (timestamps, batch preferences)
	- Installation context updates

## Data Models
```mermaid
erDiagram
	Action {
		string id PK
		string kind
		string key
		string description
		date startTime
		date endTime
		object data
		string minAppVersion
	}
	ChangeStore {
		string id PK
		string personId FK
		string deviceId FK
		timestamp syncUntil
		object data
	}
	SyncChange {
		string id PK
		string personId FK
		string deviceId FK
		timestamp timestamp
		string type
		object changes
	}
	Person {
		string id PK
		string deviceId
		object preferences
	}
	Installation {
		string id PK "deviceId"
		string personId FK
		string platform
		string version
	}
	Widget {
		string id PK
		string kind
		string key "max:32"
		string name
		object param
		boolean sync
		date startTime
		date endTime
		boolean enabled
	}
	WidgetData {
		string id PK
		string widgetId FK "unique with cardId"
		string personId FK
		string cardId FK
		string cardMasterId
		string key "max:32"
		object data "user custom data"
		string value "summary display"
		object badge "unread/valid counts"
		object tenant "CRM context"
		date createdAt
		date modifiedAt
		date deletedAt
	}
	Card {
		string id PK
		string masterId FK
		string personId FK
	}

	Person ||--o{ Action : "performs"
	Person ||--o{ ChangeStore : "owns"
	Person ||--o{ SyncChange : "has"
	Person ||--o{ Card : "owns"
	Installation ||--|| ChangeStore : "syncs"
	Installation ||--o{ SyncChange : "generates"
	Widget ||--o{ WidgetData : "has data"
	Card ||--o{ WidgetData : "contains"
	Person ||--o{ WidgetData : "owns"
```

### Key Entities

1. **Action**
	- Represents operations that can be performed in the system
	- Supports different kinds: local, remote, template, multi, intent
	- Contains execution timing and version requirements

2. **Change Store**
	- Stores synchronized data for each user/device combination
	- Manages sync timestamps and change tracking
	- Implements tenant-aware change tracking with Redis backend
	- Handles distributed storage and event propagation
	- Provides automatic cleanup through purge times

3. **SyncChange**
	- Tracks modifications made by users/devices
	- Records timestamps for incremental sync
	- Stores change details for conflict resolution

4. **Person**
	- Represents system users
	- Contains user preferences
	- Links to associated devices and actions

5. **Installation**
	- Represents device installations
	- Tracks app versions and platforms
	- Links devices to users

6. **Widget**
	- Defines UI components available in the system
	- Specifies widget type through `kind` property
	- Contains configuration parameters in `param` object
	- Controls sync capability with `sync` boolean flag
	- Manages availability through `startTime` and `endTime`
	- Can be enabled/disabled with `enabled` flag

7. **WidgetData**
	- Stores user-specific data for widgets
	- Maintains unique widget-person-card combinations through composite index
	- Stores custom user data in `data` object
	- Provides summary display through `value` field
	- Tracks notification state with `badge` object (unread/valid counts)
	- Integrates with CRM through `tenant` context
	- Supports soft deletion via `deletedAt` timestamp

8. **Card**
	- Links physical/digital cards to users and master templates
	- Acts as core entity for widget interactions
	- Connects individual cards to their master definitions

### Key Relationships

1. **Person-ChangeStore**
   - One Person can have multiple ChangeStore entries (1:N)
   - Each ChangeStore is specific to a Person-Installation pair
   - Enables multi-device synchronization
   - Cascade delete on Person deletion

2. **Person-Action**
   - Person can perform multiple Actions (1:N)
   - Actions are version tracked for sync
   - Supports version-specific availability
   - Soft delete with status field

3. **Installation-SyncChange**
   - Installation can generate multiple SyncChanges (1:N)
   - Changes tracked per device for conflict resolution
   - Version history maintained for 30 days
   - Automatic cleanup of old changes

4. **Person-Installation**
   - Person can have multiple Installations (1:N)
   - Each Installation uniquely identified by deviceId
   - Status tracking for active devices
   - Last seen tracking for cleanup

5. **Widget-WidgetData**
   - One Widget template can have multiple WidgetData instances (1:N)
   - WidgetData belongs to exactly one Widget through `widgetId`
   - Relationship enhanced with sync capabilities when Widget's `sync` is true

6. **Card-WidgetData**
   - Each Card can have multiple WidgetData instances (1:N)
   - WidgetData belongs to one Card through `cardId`
   - Maintains additional link to card template via `cardMasterId`
   - **Important**: Each unique widget-person-card combination should have only ONE widget data instance
   - This constraint is enforced through a unique composite index on `widgetId` and `cardId`

7. **Person-WidgetData**
   - Person can have multiple WidgetData instances (1:N)
   - WidgetData is associated with exactly one Person through `personId`
   - Enables personalized widget experiences

## API Endpoints
For detailed API documentation, see [API.md](docs/API.md).

### Core Sync APIs (Implemented by Sync Service)
- `POST /Cache/app/sync` - Main bi-directional sync endpoint for all models

### Sync APIs (Exposed by Backend Services, Powered by Sync Service)
- `GET /:model/app/fetch` - Targeted object retrieval for specific models, allowing clients to fetch only the objects they need by ID. This endpoint is exposed by models with the Sync mixin and requires the `syncFetch` method implementation. It accepts query parameters for IDs and optional fetch options.

- `POST /:model/app/sync` - Model-specific bi-directional sync endpoint that handles conflict resolution for individual model types. This endpoint is optional but recommended for models with complex sync requirements. It supports version-based synchronization with options for sync cutoff timestamps and fetch limits.

Both endpoints are added to models through the Sync mixin provided by the Sync Service. While they are exposed through each model's API, they leverage the Sync Service's core functionality for the actual sync process, with the Backend Services providing model-specific data access methods.

### Process Flow
```mermaid
graph TB
    %% Backend Services Layer
    subgraph BackendServices["Backend Services"]
        subgraph BackendAPILayer["API Layer"]
            ModelSync["/:model/app/sync"]
            ModelFetch["/:model/app/fetch"]
        end
        ModelSyncFetch["syncFetch Methods"]
        ModelSyncUpdate["syncUpdate Methods (optional)"]
        SyncMixin["Sync Mixin"]
    end

    %% Event Bus Layer
    EventBus["Event Bus"]

    %% Storage Layer
    subgraph Storage["Change Store"]
        Changes["Change Tracking"]
        Delta["Delta Storage"]
        State["Current State"]
    end

    %% Sync Service Layer
    subgraph SyncService["Sync Service"]
        subgraph APILayer["API Layer"]
            CacheSync["/Cache/app/sync"]
        end

        subgraph SyncCore["Sync Core"]
            SyncManager["Sync Manager"]
            ConflictRes["Conflict Resolution"]
        end
    end

    %% Mobile App Layer
    subgraph App["Perkd App"]
        Local["Local Storage"]
        Manager["Sync Manager"]
    end

    %% Part 1: Server-side Updates
    BackendServices -->|emit events| EventBus
    EventBus -->|track| Changes
    Changes -->|update| State
    Delta -->|store| State

    %% Part 2: App-initiated Sync
    Manager -->|1.POST /Cache/app/sync| CacheSync
    Manager -->|1.POST| ModelSync
    Manager -->|1.GET| ModelFetch

    %% Backend Services to Sync Service
    SyncService -->|provides| SyncMixin
    SyncMixin -->|adds endpoints to| ModelSync
    SyncMixin -->|adds endpoints to| ModelFetch
    ModelSync -->|calls| ModelSyncUpdate
    ModelSync -->|delegates to| SyncCore
    ModelFetch -->|delegates to| SyncCore
    SyncCore -->|requests data via| ModelSyncFetch

    %% Core Sync Flow
    CacheSync -->|process| SyncCore
    SyncCore -->|check| Changes
    SyncCore -->|resolve| ConflictRes
    ConflictRes -->|apply| SyncCore
    SyncCore -->|update via| BackendServices
    SyncCore -->|response| Manager
    Manager -->|persist| Local

    %% Styling
    classDef storage fill:#9673a6,stroke:#9673a6,color:white
    classDef sync fill:#2D5B2D,stroke:#2D5B2D,color:white
    classDef app fill:#6c8ebf,stroke:#6c8ebf,color:white
    classDef backend fill:#d79b00,stroke:#d79b00,color:white
    classDef apiNode fill:#d79b00,stroke:#d79b00,color:white

    class Changes,Delta,State storage
    class CacheSync,SyncManager,ConflictRes,SyncMixin sync
    class Local,Manager app
    class ModelSyncFetch,ModelSyncUpdate backend
    class ModelSync,ModelFetch apiNode
```

## Events

### Published Events
Events emitted by this service for other services to consume.

1. **Sync Events** (`sync.*`)
   - `sync.started` - Sync process initiated
   - `sync.completed` - Sync process completed successfully
   - `sync.failed` - Sync process failed
   - `sync.conflict` - Conflict detected during sync

### Subscribed Events
Events consumed by this service from other services.

1. **App Events**
   - `app.pause` - Application paused
   - `app.resume` - Application resumed
   - `app.sync` - Application sync requested
   - `app.metrics` - Application metrics reported

2. **Data Events**
   - `card.updated` - Card data updated
   - `cardMaster.updated` - Card master data updated
   - `offer.updated` - Offer data updated
   - `reward.updated` - Reward data updated
   - `message.updated` - Message data updated
   - `place.updated` - Place data updated
   - `person.updated` - Person data updated
   - `widget.updated` - Widget data updated

3. **System Events**
   - `installation.updated` - Installation status updated
   - `permission.updated` - Permission settings changed
   - `payment.updated` - Payment information updated

### Event Bus
- Consumer: 300 tokens per 2000ms
- Bus: 2000 tokens per 2000ms
- Maximum event history: 10,000 events
- Maximum batch size: 500 events

## Rate Limits
- Request body size: 10mb max
- URL encoded limit: 100kb
- Per endpoint limits:
  - Sync operations: 60 requests/minute
  - Message operations: 30 requests/minute
- Batch size limits:
  - Maximum objects per sync request: 500 objects
  - Maximum event batch size: 500 events
- Cache limits:
  - Maximum instances loaded into cache: 20,000 per store
  - Configurable through LIMIT constant in SyncModule:
  ```javascript
  const LIMIT = 20000;  // max instances loaded into cache
  ```
- Rate limiting is enforced at the API layer with appropriate status codes and retry-after headers

## Available Metrics
- Sync operations per minute
- Cache hit/miss ratio
- API response times
- Error rates

### Batch Limits
- Maximum event batch size: 500 events
- Maximum object sync size: 500 objects per request
