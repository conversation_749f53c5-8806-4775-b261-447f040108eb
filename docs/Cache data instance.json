{"id": "651a968c55c272d801f1395c", "personId": "64749fdffe89be003a428a90", "deviceId": "613740C2-67E9-43C7-B2BD-3A6CB0A24DB3", "objects": [{"model": "Person", "lastSync": 1696298945697.0, "old": {"id": "64749fdffe89be003a428a90", "familyName": "<PERSON>", "givenName": "Li", "alias": "", "name": {"displayAs": "familygiven", "order": "familygiven", "salutation": null}, "gender": "f", "ethnicity": 101, "religion": null, "dateList": [{"id": "42f81543c8fde91731fe44e3", "name": "birth", "date": "1987-05-29T00:00:00.000Z", "year": 1987, "month": 5, "day": 29}], "emailList": [{"id": "li8vt4y5", "address": "zhang<PERSON>@waveo.com", "type": "home", "optIn": null, "valid": true}], "phoneList": [{"id": "dee8e23a0ac9ab961670770c", "fullNumber": "6590490001", "type": "mobile", "countryCode": "65", "areaCode": "", "number": "90490001", "regionCode": "SG", "lineType": "mobile", "optIn": null, "valid": true}], "addressList": [{"id": "ln9ohsjr", "type": "others", "formatted": "247 Ponggol Seventeenth Ave, Singapore 829704", "short": "247 Ponggol Seventeenth Ave", "optIn": null, "valid": null, "unit": null, "level": null, "house": null, "street": "247 Ponggol Seventeenth Ave", "city": "Singapore", "state": null, "postCode": "829704", "country": "SG", "geo": {"type": "Point", "coordinates": [103.9039623, 1.4162192]}, "label": "Office"}], "brands": {"favorites": ["glico", "fuwan"], "nointerest": [], "recommend": []}, "createdAt": "2023-05-29T12:51:43.710Z", "modifiedAt": "2023-10-03T02:06:18.464Z", "deletedAt": null, "image": null, "_model": "Person"}, "new": {"id": "64749fdffe89be003a428a90", "familyName": "<PERSON>", "givenName": "Li", "alias": "", "name": {"displayAs": "familygiven", "order": "familygiven", "salutation": null}, "gender": "f", "ethnicity": 101, "religion": null, "dateList": [{"id": "42f81543c8fde91731fe44e3", "name": "birth", "date": "1987-05-29T00:00:00.000Z", "year": 1987, "month": 5, "day": 29}], "emailList": [{"id": "li8vt4y5", "address": "zhang<PERSON>@waveo.com", "type": "home", "optIn": null, "valid": true}], "phoneList": [{"id": "dee8e23a0ac9ab961670770c", "fullNumber": "6590490001", "type": "mobile", "countryCode": "65", "areaCode": "", "number": "90490001", "regionCode": "SG", "lineType": "mobile", "optIn": null, "valid": true}], "addressList": [{"id": "ln9ohsjr", "type": "others", "formatted": "247 Ponggol Seventeenth Ave, Singapore 829704", "short": "247 Ponggol Seventeenth Ave", "optIn": null, "valid": null, "unit": null, "level": null, "house": null, "street": "247 Ponggol Seventeenth Ave", "city": "Singapore", "state": null, "postCode": "829704", "country": "SG", "geo": {"type": "Point", "coordinates": [103.9039623, 1.4162192]}, "label": "Office"}], "brands": {"favorites": ["glico", "fuwan"], "nointerest": [], "recommend": []}, "createdAt": "2023-05-29T12:51:43.710Z", "modifiedAt": "2023-10-03T02:06:18.464Z", "deletedAt": null, "image": null, "_model": "Person"}}, {"model": "AccountSetting", "lastSync": *************.0, "old": {"id": "6474a0a0970808003ac7823f", "reminders": {"card": {"kind": "expiry", "days": 14, "timeOfDay": ********}, "offer": {"kind": "expiry", "days": 7, "timeOfDay": ********}}, "discover": {"cards": true, "regions": [{"id": "1", "type": "country", "name": "Singapore", "country": "SG"}]}, "settings": {}, "bioAuth": true, "scan": true, "logs": {"severity": 2}, "card": {"view": "big", "order": "custom", "displayName": null, "views": {"6474a0649547ca003b808e15": 1, "6474a0a09547ca003b808e16": 0, "649bef8584a5bb003ae0d2b7": 1, "64a2e4822c0073003a54e48e": 0, "64a2e976c6c1a0003ba1e07d": 0, "64a2ed842c0073003a54e490": 1, "64a2ee852c0073003a54e492": 2, "64a2eeef2c0073003a54e494": 4, "64e057f7ac8c11003c42d417": 1, "6511b5c4426744003a799527": 3, "6511b5fa426744003a799528": 2, "651a96ddb11d50003c325d66": 5}, "sort": ["651a96ddb11d50003c325d66", "6511b5fa426744003a799528", "6511b5c4426744003a799527", "64e057f7ac8c11003c42d417", "64a2eeef2c0073003a54e494", "64a2ee852c0073003a54e492", "64a2ed842c0073003a54e490", "64a2e976c6c1a0003ba1e07d", "64a2e4822c0073003a54e48e", "649bef8584a5bb003ae0d2b7", "6474a0a09547ca003b808e16", "6474a0649547ca003b808e15"]}, "payments": {}, "social": {}, "createdAt": "2023-05-29T12:54:56.045Z", "modifiedAt": "2023-10-03T02:09:35.857Z", "deletedAt": null, "_model": "AccountSetting"}, "new": null}, {"model": "Place", "lastSync": *************.0, "old": {"name": "Office", "brand": {"short": "", "long": "", "color": null, "logo": null}, "geo": {"type": "Point", "coordinates": [103.903962, 1.416219]}, "type": "work", "custom": true, "cardId": "6474a0649547ca003b808e15", "personId": "64749fdffe89be003a428a90", "startTime": null, "endTime": null, "openingHours": {"periods": []}, "pickup": {}, "external": {}, "locale": {"languages": [], "currency": null, "country": null, "timeZone": null, "regions": []}, "addressList": [{"id": "ln9nm1er", "type": "store", "house": "247", "level": "", "unit": "", "street": "Ponggol Seventeenth Avenue", "city": "Singapore", "state": null, "postCode": "829704", "country": "SG", "formatted": "247 Ponggol Seventeenth Ave, Singapore 829704", "short": "247 Ponggol Seventeenth", "geo": {"type": "Point", "coordinates": [103.903962, 1.416219]}, "optIn": null, "valid": null}], "createdAt": "2023-10-03T01:41:34.754Z", "modifiedAt": "2023-10-03T01:41:34.754Z", "id": "651b714e1c34570000e85e23"}, "new": null}, {"model": "Card", "lastSync": 1696241294461.0, "old": {"id": "64a2eeef2c0073003a54e494", "number": "FEMIS000103", "barcodeType": "QRCODE", "displayName": "<PERSON>", "startTime": "2023-07-03T15:53:19.222Z", "endTime": "2024-07-01T15:59:59.999Z", "flow": {"at": 2, "steps": ["request", "register", "done"]}, "state": "active", "imageIndex": 0, "formData": {"nameOrder": "familygiven", "familyName": "<PERSON>", "givenName": "Li", "gender": "f", "birth": {"year": 1969, "month": 12, "day": 6}, "mobile": "6590490001", "mobileOptIn": true, "email": "zhang<PERSON>@waveo.com", "emailOptIn": true, "displayName": "<PERSON>"}, "forms": {}, "credentials": {}, "custom": {}, "reminders": {}, "triggers": {}, "options": {"hideZeroBalance": true}, "sharer": null, "shareModes": ["invite"], "preIssued": false, "useCustomImage": false, "storedValue": {"balance": 0, "currency": {"code": "SGD", "precision": 2}}, "when": {"issued": "2023-07-03T15:53:18.563Z", "notified": "2023-07-03T15:53:18.563Z", "declined": null, "accepted": "2023-07-03T15:53:18.563Z", "paid": null, "registered": "2023-07-03T15:53:18.563Z", "activated": null, "transferred": null, "revoked": null, "cancelled": null, "terminated": null, "updated": null}, "reasons": {}, "acquired": {"type": "perkd", "format": "lib", "touchedAt": "2023-07-03T15:53:18.563Z"}, "createdAt": "2023-07-03T15:53:20.302Z", "modifiedAt": "2023-07-03T15:53:21.985Z", "deletedAt": null, "masterId": "6345469aa56c87001d9107a8", "personId": "64749fdffe89be003a428a90", "cardImage": {"irregularShape": false, "transparency": false}, "sharings": [], "image": null, "_model": "Card"}, "new": {"id": "64a2eeef2c0073003a54e494", "number": "FEMIS000103", "barcodeType": "QRCODE", "displayName": "<PERSON>", "startTime": "2023-07-03T15:53:19.222Z", "endTime": "2024-07-01T15:59:59.999Z", "flow": {"at": 2, "steps": ["request", "register", "done"]}, "state": "active", "imageIndex": 0, "formData": {"nameOrder": "familygiven", "familyName": "<PERSON>", "givenName": "Li", "gender": "f", "birth": {"year": 1969, "month": 12, "day": 6}, "mobile": "6590490001", "mobileOptIn": true, "email": "zhang<PERSON>@waveo.com", "emailOptIn": true, "displayName": "<PERSON>"}, "forms": {}, "credentials": {}, "custom": {}, "reminders": {}, "triggers": {}, "options": {"hideZeroBalance": true}, "sharer": null, "shareModes": ["invite"], "preIssued": false, "useCustomImage": false, "storedValue": {"balance": 0, "currency": {"code": "SGD", "precision": 2}}, "when": {"issued": "2023-07-03T15:53:18.563Z", "notified": "2023-07-03T15:53:18.563Z", "declined": null, "accepted": "2023-07-03T15:53:18.563Z", "paid": null, "registered": "2023-07-03T15:53:18.563Z", "activated": null, "transferred": null, "revoked": null, "cancelled": null, "terminated": null, "updated": null}, "reasons": {}, "acquired": {"type": "perkd", "format": "lib", "touchedAt": "2023-07-03T15:53:18.563Z"}, "createdAt": "2023-07-03T15:53:20.302Z", "modifiedAt": "2023-07-03T15:53:21.985Z", "deletedAt": null, "masterId": "6345469aa56c87001d9107a8", "personId": "64749fdffe89be003a428a90", "cardImage": {"irregularShape": false, "transparency": false}, "sharings": [], "image": null, "_model": "Card"}, "syncUntil": 0}, {"model": "Message", "lastSync": 1696298945697.0, "old": {"id": "651b772c882cc1003acf0b53", "kind": "receipt", "sender": {"name": "<PERSON><PERSON>'s Tea"}, "subject": "", "preview": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/rsrc/05975000-75e0-11ed-aebf-a9f6cc78c16d-push.png", "defaultLanguage": "en", "cover": {"html": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/html/en/cover.html", "resources": [{"uri": "https://cdn.perkd.me/shared/js/appbridge-1.0.min.js", "key": "appbridge.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/vue-2.6.10.min.js", "key": "vue-2.6.10.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/moment-2.24.0.min.js", "key": "moment-2.24.0.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/appletHelper-1.0.min.js", "key": "appletHelper-1.0.min.js", "shared": true, "required": false, "type": "js"}], "param": {"styles": {"backgroundColor": "rgba(255,255,255,1)"}, "props": {"bounces": false}, "dimension": {"width": 320, "height": 180}}}, "body": {"html": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/html/en/body.html", "resources": [{"uri": "https://cdn.perkd.me/shared/js/appbridge-1.0.min.js", "key": "appbridge.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/rsrc/ca3ccbd8-4e93-4bf1-8d63-a19728881097-standard.png", "key": "636c284dd40f31001ea6ff3e", "size": 47061, "shared": false, "required": false, "type": "image"}, {"uri": "https://cdn.perkd.me/shared/js/vue-2.6.10.min.js", "key": "vue-2.6.10.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/moment-2.24.0.min.js", "key": "moment-2.24.0.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/appletHelper-1.0.min.js", "key": "appletHelper-1.0.min.js", "shared": true, "required": false, "type": "js"}], "param": {"styles": {"backgroundColor": "rgba(255,255,255,1)"}, "props": {"bounces": true}}}, "personalize": null, "triggers": {}, "localStates": {"custom": {}, "order": {"id": "651b771a1c34570000e85e56", "receipt": {"number": "P-VJBUA5CDXY", "send": true}, "currency": "SGD", "items": [{"id": "ln9oi6ig", "product": {"id": "63592db555c5b6afa122e269", "bundleList": [], "title": "Black Sugar Fresh Milk", "brand": "femistea-dev", "external": {"provider": "shopify", "productId": "7006305190020"}, "tags": {"category": ["Beverages"], "keyword": ["bestseller"]}, "behaviors": {}, "variationList": []}, "variant": {"id": "63592db555c5b6afa122e26e", "productId": "63592db555c5b6afa122e269", "kind": "physical", "title": "Black Sugar Fresh Milk", "gtin": {}, "sku": "fresh-milk-blacksugar", "inventory": {"management": "shopify", "policy": "deny", "lowQuantityWarningThreshold": 0, "quantity": 9634}, "prices": [{"name": "base", "price": {"value": 4.9}, "salePrice": {"value": 4.9}, "paymentMethods": [], "countries": []}], "taxable": true, "options": [], "fulfillmentService": "kitchen", "preparation": {"time": 180}, "external": {"provider": "shopify", "variantId": "41097288941700"}, "imageIds": []}, "kind": "product", "variantOptions": [{"name": "Size", "values": [{"name": "Standard", "quantity": 1}], "key": "size", "type": "tagList", "required": true, "max": 1, "min": 1}, {"name": "Sweetness", "values": [{"name": "100%", "quantity": 1}], "key": "sweetness", "type": "slider", "required": true}, {"name": "Ice level", "values": [{"name": "Regular", "quantity": 1}], "key": "ice", "type": "radioboxList", "max": 1, "min": 1, "required": true}, {"name": "Toppings", "values": [], "key": "toppings1", "type": "checkboxList", "max": 3, "min": 0, "required": false}], "quantity": 1, "units": 1, "discountAmount": 0, "amount": 4.9, "unitPrice": 4.9, "images": ["https://femistea-dev.myshopify.com/cdn/shop/products/27.BlackSugarFreshMilk.png?v=**********"], "properties": []}], "totalDiscount": 0, "totalTax": 0.73, "subtotalPrice": 4.9, "totalPrice": 9.9, "shippingPrice": 5, "quantity": 1, "external": {"perkd": {"userId": "64749fdffe89be003a428a90", "cardId": "64a2eeef2c0073003a54e494", "messageId": "651b772c882cc1003acf0b53"}, "shopify": {"customerId": "5842997477508", "orderId": "*************"}}, "flow": {"at": 0, "steps": ["received", "allocated", "collected", "delivered"]}, "when": {"received": "2023-10-03T02:06:32.762Z", "paid": "2023-10-03T02:06:28.946Z", "packed": null, "collected": null, "dispatched": null, "delivered": null, "accepted": null, "declined": null, "fulfilled": null, "returned": null, "cancelled": null}, "discountList": [], "billingList": [{"id": "ln9oi6if", "paymentMethod": {"method": "card", "processor": "stripe", "wallet": "applepay", "transactionId": "pi_3Nwy0JQ0JtHb8B6S0om9xgXr", "amount": 9.9, "status": "paid", "transactions": [{"type": "intent", "amount": 9.9, "currency": "SGD", "method": "card", "provider": "stripe", "referenceId": "pi_3Nwy0JQ0JtHb8B6S0om9xgXr", "status": "paid", "action": {}, "createdAt": "2023-10-03T02:06:32.522Z", "id": "651b77281a5986003a7e678e", "details": {"id": "pi_3Nwy0JQ0JtHb8B6S0om9xgXr"}}], "createdAt": "2023-10-03T02:06:28.946Z", "intent": {"paymentMethodId": "pm_1Nwy0FQ0JtHb8B6SXmsFBm9M"}}, "currency": {"userCurrency": "SGD"}}]}, "fulfillment": {"type": "deliver", "priority": "normal", "recipient": {"fullName": "<PERSON>", "phone": "9049 0001", "email": "zhang<PERSON>@waveo.com", "pickupTime": "3 Oct, 7pm - 8pm", "postCode": "829704", "gender": "f", "countryCode": "65"}, "destination": {"street": "247 Ponggol Seventeenth Ave", "city": "Singapore", "postCode": "829704", "country": "SG", "geo": {"type": "Point", "coordinates": [103.9039623, 1.4162192]}, "id": "ln9ohsjr", "formatted": "247 Ponggol Seventeenth Ave, Singapore 829704", "short": "247 Ponggol Seventeenth Ave", "type": "others", "label": "Office"}, "origin": {}, "scheduled": {"minTime": "2023-10-03T11:00:00.184Z", "maxTime": "2023-10-03T12:00:00.184Z"}, "status": "open", "flow": {"at": 1, "steps": ["ordered", "requested", "allocated", "collected", "delivered"]}, "external": {"perkd": {"cardId": "64a2eeef2c0073003a54e494"}, "lalamove": {"orderId": "146680406684"}}, "minTime": "2023-10-03T10:40:00.184Z"}}, "reminders": {}, "options": {}, "state": "active", "when": {"sent": "2023-10-03T02:06:36.185Z", "issued": null, "received": "2023-10-03T02:06:39.119Z", "notified": "2023-10-03T02:06:36.185Z", "seen": null, "viewed": null, "rsrcUpdated": null}, "activeTime": "2023-10-03T02:06:36.185Z", "purgeTime": "2024-01-01T02:06:36.185Z", "globalize": {"t": {"en": {"preview": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/rsrc/05975000-75e0-11ed-aebf-a9f6cc78c16d-push.png", "subject": "", "cover": {"html": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/html/en/cover.html", "resources": [{"uri": "https://cdn.perkd.me/shared/js/appbridge-1.0.min.js", "key": "appbridge.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/vue-2.6.10.min.js", "key": "vue-2.6.10.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/moment-2.24.0.min.js", "key": "moment-2.24.0.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/appletHelper-1.0.min.js", "key": "appletHelper-1.0.min.js", "shared": true, "required": false, "type": "js"}], "param": {"styles": {"backgroundColor": "rgba(255,255,255,1)"}, "props": {"bounces": false}, "dimension": {"width": 320, "height": 180}}}, "body": {"html": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/html/en/body.html", "resources": [{"uri": "https://cdn.perkd.me/shared/js/appbridge-1.0.min.js", "key": "appbridge.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/rsrc/ca3ccbd8-4e93-4bf1-8d63-a19728881097-standard.png", "key": "636c284dd40f31001ea6ff3e", "size": 47061, "shared": false, "required": false, "type": "image"}, {"uri": "https://cdn.perkd.me/shared/js/vue-2.6.10.min.js", "key": "vue-2.6.10.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/moment-2.24.0.min.js", "key": "moment-2.24.0.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/appletHelper-1.0.min.js", "key": "appletHelper-1.0.min.js", "shared": true, "required": false, "type": "js"}], "param": {"styles": {"backgroundColor": "rgba(255,255,255,1)"}, "props": {"bounces": true}}}}}, "default": "en"}, "createdAt": "2023-10-03T02:06:36.224Z", "modifiedAt": "2023-10-03T02:06:40.533Z", "deletedAt": null, "templateId": "63bf9436ba609dbfe09c257b", "personId": "64749fdffe89be003a428a90", "cardId": "64a2eeef2c0073003a54e494", "_model": "Message"}, "new": {"id": "651b772c882cc1003acf0b53", "kind": "receipt", "sender": {"name": "<PERSON><PERSON>'s Tea"}, "subject": "", "preview": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/rsrc/05975000-75e0-11ed-aebf-a9f6cc78c16d-push.png", "defaultLanguage": "en", "cover": {"html": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/html/en/cover.html", "resources": [{"uri": "https://cdn.perkd.me/shared/js/appbridge-1.0.min.js", "key": "appbridge.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/vue-2.6.10.min.js", "key": "vue-2.6.10.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/moment-2.24.0.min.js", "key": "moment-2.24.0.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/appletHelper-1.0.min.js", "key": "appletHelper-1.0.min.js", "shared": true, "required": false, "type": "js"}], "param": {"styles": {"backgroundColor": "rgba(255,255,255,1)"}, "props": {"bounces": false}, "dimension": {"width": 320, "height": 180}}}, "body": {"html": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/html/en/body.html", "resources": [{"uri": "https://cdn.perkd.me/shared/js/appbridge-1.0.min.js", "key": "appbridge.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/rsrc/ca3ccbd8-4e93-4bf1-8d63-a19728881097-standard.png", "key": "636c284dd40f31001ea6ff3e", "size": 47061, "shared": false, "required": false, "type": "image"}, {"uri": "https://cdn.perkd.me/shared/js/vue-2.6.10.min.js", "key": "vue-2.6.10.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/moment-2.24.0.min.js", "key": "moment-2.24.0.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/appletHelper-1.0.min.js", "key": "appletHelper-1.0.min.js", "shared": true, "required": false, "type": "js"}], "param": {"styles": {"backgroundColor": "rgba(255,255,255,1)"}, "props": {"bounces": true}}}, "personalize": null, "triggers": {}, "localStates": {"custom": {}, "order": {"id": "651b771a1c34570000e85e56", "receipt": {"number": "P-VJBUA5CDXY", "send": true}, "currency": "SGD", "items": [{"id": "ln9oi6ig", "product": {"id": "63592db555c5b6afa122e269", "bundleList": [], "title": "Black Sugar Fresh Milk", "brand": "femistea-dev", "external": {"provider": "shopify", "productId": "7006305190020"}, "tags": {"category": ["Beverages"], "keyword": ["bestseller"]}, "behaviors": {}, "variationList": []}, "variant": {"id": "63592db555c5b6afa122e26e", "productId": "63592db555c5b6afa122e269", "kind": "physical", "title": "Black Sugar Fresh Milk", "gtin": {}, "sku": "fresh-milk-blacksugar", "inventory": {"management": "shopify", "policy": "deny", "lowQuantityWarningThreshold": 0, "quantity": 9634}, "prices": [{"name": "base", "price": {"value": 4.9}, "salePrice": {"value": 4.9}, "paymentMethods": [], "countries": []}], "taxable": true, "options": [], "fulfillmentService": "kitchen", "preparation": {"time": 180}, "external": {"provider": "shopify", "variantId": "41097288941700"}, "imageIds": []}, "kind": "product", "variantOptions": [{"name": "Size", "values": [{"name": "Standard", "quantity": 1}], "key": "size", "type": "tagList", "required": true, "max": 1, "min": 1}, {"name": "Sweetness", "values": [{"name": "100%", "quantity": 1}], "key": "sweetness", "type": "slider", "required": true}, {"name": "Ice level", "values": [{"name": "Regular", "quantity": 1}], "key": "ice", "type": "radioboxList", "max": 1, "min": 1, "required": true}, {"name": "Toppings", "values": [], "key": "toppings1", "type": "checkboxList", "max": 3, "min": 0, "required": false}], "quantity": 1, "units": 1, "discountAmount": 0, "amount": 4.9, "unitPrice": 4.9, "images": ["https://femistea-dev.myshopify.com/cdn/shop/products/27.BlackSugarFreshMilk.png?v=**********"], "properties": []}], "totalDiscount": 0, "totalTax": 0.73, "subtotalPrice": 4.9, "totalPrice": 9.9, "shippingPrice": 5, "quantity": 1, "external": {"perkd": {"userId": "64749fdffe89be003a428a90", "cardId": "64a2eeef2c0073003a54e494", "messageId": "651b772c882cc1003acf0b53"}, "shopify": {"customerId": "5842997477508", "orderId": "*************"}}, "flow": {"at": 0, "steps": ["received", "allocated", "collected", "delivered"]}, "when": {"received": "2023-10-03T02:06:32.762Z", "paid": "2023-10-03T02:06:28.946Z", "packed": null, "collected": null, "dispatched": null, "delivered": null, "accepted": null, "declined": null, "fulfilled": null, "returned": null, "cancelled": null}, "discountList": [], "billingList": [{"id": "ln9oi6if", "paymentMethod": {"method": "card", "processor": "stripe", "wallet": "applepay", "transactionId": "pi_3Nwy0JQ0JtHb8B6S0om9xgXr", "amount": 9.9, "status": "paid", "transactions": [{"type": "intent", "amount": 9.9, "currency": "SGD", "method": "card", "provider": "stripe", "referenceId": "pi_3Nwy0JQ0JtHb8B6S0om9xgXr", "status": "paid", "action": {}, "createdAt": "2023-10-03T02:06:32.522Z", "id": "651b77281a5986003a7e678e", "details": {"id": "pi_3Nwy0JQ0JtHb8B6S0om9xgXr"}}], "createdAt": "2023-10-03T02:06:28.946Z", "intent": {"paymentMethodId": "pm_1Nwy0FQ0JtHb8B6SXmsFBm9M"}}, "currency": {"userCurrency": "SGD"}}]}, "fulfillment": {"type": "deliver", "priority": "normal", "recipient": {"fullName": "<PERSON>", "phone": "9049 0001", "email": "zhang<PERSON>@waveo.com", "pickupTime": "3 Oct, 7pm - 8pm", "postCode": "829704", "gender": "f", "countryCode": "65"}, "destination": {"street": "247 Ponggol Seventeenth Ave", "city": "Singapore", "postCode": "829704", "country": "SG", "geo": {"type": "Point", "coordinates": [103.9039623, 1.4162192]}, "id": "ln9ohsjr", "formatted": "247 Ponggol Seventeenth Ave, Singapore 829704", "short": "247 Ponggol Seventeenth Ave", "type": "others", "label": "Office"}, "origin": {}, "scheduled": {"minTime": "2023-10-03T11:00:00.184Z", "maxTime": "2023-10-03T12:00:00.184Z"}, "status": "open", "flow": {"at": 1, "steps": ["ordered", "requested", "allocated", "collected", "delivered"]}, "external": {"perkd": {"cardId": "64a2eeef2c0073003a54e494"}, "lalamove": {"orderId": "146680406684"}}, "minTime": "2023-10-03T10:40:00.184Z"}}, "reminders": {}, "options": {}, "state": "active", "when": {"sent": "2023-10-03T02:06:36.185Z", "issued": null, "received": "2023-10-03T02:06:39.119Z", "notified": "2023-10-03T02:06:36.185Z", "seen": null, "viewed": null, "rsrcUpdated": null}, "activeTime": "2023-10-03T02:06:36.185Z", "purgeTime": "2024-01-01T02:06:36.185Z", "globalize": {"t": {"en": {"preview": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/rsrc/05975000-75e0-11ed-aebf-a9f6cc78c16d-push.png", "subject": "", "cover": {"html": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/html/en/cover.html", "resources": [{"uri": "https://cdn.perkd.me/shared/js/appbridge-1.0.min.js", "key": "appbridge.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/vue-2.6.10.min.js", "key": "vue-2.6.10.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/moment-2.24.0.min.js", "key": "moment-2.24.0.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/appletHelper-1.0.min.js", "key": "appletHelper-1.0.min.js", "shared": true, "required": false, "type": "js"}], "param": {"styles": {"backgroundColor": "rgba(255,255,255,1)"}, "props": {"bounces": false}, "dimension": {"width": 320, "height": 180}}}, "body": {"html": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/html/en/body.html", "resources": [{"uri": "https://cdn.perkd.me/shared/js/appbridge-1.0.min.js", "key": "appbridge.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdnstaging.perkd.me/template/63bf9436ba609dbfe09c257b/rsrc/ca3ccbd8-4e93-4bf1-8d63-a19728881097-standard.png", "key": "636c284dd40f31001ea6ff3e", "size": 47061, "shared": false, "required": false, "type": "image"}, {"uri": "https://cdn.perkd.me/shared/js/vue-2.6.10.min.js", "key": "vue-2.6.10.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/moment-2.24.0.min.js", "key": "moment-2.24.0.min.js", "shared": true, "required": false, "type": "js"}, {"uri": "https://cdn.perkd.me/shared/js/appletHelper-1.0.min.js", "key": "appletHelper-1.0.min.js", "shared": true, "required": false, "type": "js"}], "param": {"styles": {"backgroundColor": "rgba(255,255,255,1)"}, "props": {"bounces": true}}}}}, "default": "en"}, "createdAt": "2023-10-03T02:06:36.224Z", "modifiedAt": "2023-10-03T02:06:40.533Z", "deletedAt": null, "templateId": "63bf9436ba609dbfe09c257b", "personId": "64749fdffe89be003a428a90", "cardId": "64a2eeef2c0073003a54e494", "_model": "Message"}, "syncUntil": 0}, {"model": "Offer", "lastSync": 0, "syncUntil": 0, "old": null, "new": {"id": "651b780d79c01f003a8e5233", "kind": "discount", "style": {"light": {}, "dark": {}}, "masterId": "6360eabba9cc37001d6032c2", "name": "$2 OFF", "brand": "<PERSON><PERSON>'s Tea", "title": "Welcome 🥳", "description": "Enjoy $2 off at <PERSON><PERSON>'s Tea when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the promotions without prior notice.&nbsp;</li></ul></div></body></html>", "code": {"store": "7SV56ZSX", "online": "7SV56ZSX", "instore": "7SV56ZSX"}, "barcodeType": "CODE128", "barcode": "7SV56ZSX", "startTime": "2023-10-03T02:10:20.964Z", "endTime": "2023-10-17T15:59:59.999Z", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/offer/ef25ab10-915e-11ed-a2cb-d359291aeb5d.png"}], "discount": {"name": "$2 OFF", "kind": "fixed", "value": 2, "currency": "SGD", "use": "combine", "priority": 6, "excludeOffers": [], "qualifiers": {}, "targetType": "item", "targetSelection": "all", "allocation": {"method": "across", "limit": 1}, "entitled": {}}, "redemption": {"limit": 1, "remain": 1, "channels": ["perkd"]}, "reminders": {}, "triggers": {}, "options": {"redeemOnline": false, "hideButton": false, "buttonLink": null, "appOnly": false}, "fields": [], "state": "active", "shareModes": [], "globalize": {"t": {"en": {"name": "$2 OFF", "shortName": "$2 OFF", "title": "Welcome 🥳", "description": "Enjoy $2 off at <PERSON><PERSON>'s Tea when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the promotions without prior notice.&nbsp;</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/offer/ef25ab10-915e-11ed-a2cb-d359291aeb5d.png"}]}}, "default": "en"}, "activeTime": "2022-10-31T16:00:00.000Z", "purgeTime": "2024-01-15T15:59:59.999Z", "when": {"sent": "2023-10-03T02:10:20.977Z", "issued": "2023-10-03T02:10:20.977Z", "received": "2023-10-03T02:10:23.833Z", "notified": "2023-10-03T02:10:25.610Z", "viewed": null, "redeemed": null, "cancelled": null, "recovered": null, "transferred": null}, "reasons": {}, "createdAt": "2023-10-03T02:10:21.423Z", "modifiedAt": "2023-10-03T02:10:26.087Z", "deletedAt": null, "cardId": "64a2eeef2c0073003a54e494", "personId": "64749fdffe89be003a428a90", "sharings": [], "_model": "Offer"}}, {"model": "<PERSON><PERSON>", "lastSync": 1696298945697.0, "old": {"id": "64a2eef1ad7839003ae86301", "masterId": "63614820f4479a001d56b3c0", "name": "<PERSON><PERSON><PERSON>", "brand": "<PERSON><PERSON>'s Tea", "startTime": "2023-07-03T15:53:19.222Z", "endTime": "2024-07-01T15:59:59.999Z", "logoImage": {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/logo/b0ac9180-5a01-11ed-8f14-f918db700f9d.png"}, "qualifiers": {"issuestamps": "{\"totalPrice\":{\"$gte\":6}}"}, "levels": [{"name": "$3 OFF", "level": 1, "description": "Collect 4 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "startTime": "2023-07-03T15:53:19.222Z", "endTime": "2024-07-01T15:59:59.999Z", "imageNdx": 0, "stamps": [{"name": "Stamp", "stamped": true, "issuedAt": "2023-10-03T02:06:36.256Z", "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}], "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/f397ea80-5a01-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6d35bd0-5a01-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6bceda0-5a01-11ed-8f14-f918db700f9d.png"}], "issuedAt": "2023-07-03T15:53:21.013Z", "completedAt": null, "fullyRedeemedAt": null, "offerIds": []}, {"name": "$5 OFF", "level": 2, "description": "Collect 6 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "startTime": "2023-07-03T15:53:19.222Z", "endTime": "2024-07-01T15:59:59.999Z", "imageNdx": 0, "stamps": [{"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}], "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/38628ad0-5a02-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6d35bd0-5a01-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6bceda0-5a01-11ed-8f14-f918db700f9d.png"}], "issuedAt": "2023-07-03T15:53:21.013Z", "completedAt": null, "fullyRedeemedAt": null, "offerIds": []}, {"name": "$10 OFF", "level": 3, "description": "Collect 8 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "startTime": "2023-07-03T15:53:19.222Z", "endTime": "2024-07-01T15:59:59.999Z", "imageNdx": 0, "stamps": [{"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}], "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/44989c40-5a02-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6d35bd0-5a01-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6bceda0-5a01-11ed-8f14-f918db700f9d.png"}], "issuedAt": "2023-07-03T15:53:21.013Z", "completedAt": null, "fullyRedeemedAt": null, "offerIds": []}], "transactions": [{"id": "S-ypxnu3l-", "type": "issue", "quantity": 1, "level": 1, "notifiedAt": "2023-10-03T02:06:59.263Z", "createdAt": "2023-10-03T02:06:36.256Z"}], "reminders": {}, "triggers": {}, "options": {"manual": false}, "state": "active", "shareModes": [], "globalize": {"t": {"en": {"name": "<PERSON><PERSON><PERSON>", "shortName": "<PERSON><PERSON><PERSON>", "levels": {"0": {"name": "$3 OFF", "description": "Collect 4 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/f397ea80-5a01-11ed-8f14-f918db700f9d.png"}]}, "1": {"name": "$5 OFF", "description": "Collect 6 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/38628ad0-5a02-11ed-8f14-f918db700f9d.png"}]}, "2": {"name": "$10 OFF", "description": "Collect 8 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/44989c40-5a02-11ed-8f14-f918db700f9d.png"}]}}}}, "default": "en"}, "purgeTime": "2025-07-01T15:59:59.999Z", "when": {"sent": "2023-07-03T15:53:21.260Z", "issued": "2023-07-03T15:53:21.260Z", "received": "2023-07-03T15:53:23.129Z", "notified": null, "viewed": null, "extended": null, "withdrawn": null, "completed": null, "cancelled": null, "recovered": null}, "createdAt": "2023-07-03T15:53:21.693Z", "modifiedAt": "2023-10-03T02:06:59.771Z", "deletedAt": null, "cardId": "64a2eeef2c0073003a54e494", "personId": "64749fdffe89be003a428a90", "channels": [], "_model": "<PERSON><PERSON>"}, "new": {"id": "64a2eef1ad7839003ae86301", "masterId": "63614820f4479a001d56b3c0", "name": "<PERSON><PERSON><PERSON>", "brand": "<PERSON><PERSON>'s Tea", "startTime": "2023-07-03T15:53:19.222Z", "endTime": "2024-07-01T15:59:59.999Z", "logoImage": {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/logo/b0ac9180-5a01-11ed-8f14-f918db700f9d.png"}, "qualifiers": {"issuestamps": "{\"totalPrice\":{\"$gte\":6}}"}, "levels": [{"name": "$3 OFF", "level": 1, "description": "Collect 4 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "startTime": "2023-07-03T15:53:19.222Z", "endTime": "2024-07-01T15:59:59.999Z", "imageNdx": 0, "stamps": [{"name": "Stamp", "stamped": true, "issuedAt": "2023-10-03T02:06:36.256Z", "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}], "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/f397ea80-5a01-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6d35bd0-5a01-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6bceda0-5a01-11ed-8f14-f918db700f9d.png"}], "issuedAt": "2023-07-03T15:53:21.013Z", "completedAt": null, "fullyRedeemedAt": null, "offerIds": []}, {"name": "$5 OFF", "level": 2, "description": "Collect 6 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "startTime": "2023-07-03T15:53:19.222Z", "endTime": "2024-07-01T15:59:59.999Z", "imageNdx": 0, "stamps": [{"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}], "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/38628ad0-5a02-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6d35bd0-5a01-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6bceda0-5a01-11ed-8f14-f918db700f9d.png"}], "issuedAt": "2023-07-03T15:53:21.013Z", "completedAt": null, "fullyRedeemedAt": null, "offerIds": []}, {"name": "$10 OFF", "level": 3, "description": "Collect 8 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "startTime": "2023-07-03T15:53:19.222Z", "endTime": "2024-07-01T15:59:59.999Z", "imageNdx": 0, "stamps": [{"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}, {"name": "Stamp", "stamped": false, "issuedAt": null, "bgImageNdx": 1, "stampImageNdx": 2}], "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/44989c40-5a02-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6d35bd0-5a01-11ed-8f14-f918db700f9d.png"}, {"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/stamp/a6bceda0-5a01-11ed-8f14-f918db700f9d.png"}], "issuedAt": "2023-07-03T15:53:21.013Z", "completedAt": null, "fullyRedeemedAt": null, "offerIds": []}], "transactions": [{"id": "S-ypxnu3l-", "type": "issue", "quantity": 1, "level": 1, "notifiedAt": "2023-10-03T02:06:59.263Z", "createdAt": "2023-10-03T02:06:36.256Z"}], "reminders": {}, "triggers": {}, "options": {"manual": false}, "state": "active", "shareModes": [], "globalize": {"t": {"en": {"name": "<PERSON><PERSON><PERSON>", "shortName": "<PERSON><PERSON><PERSON>", "levels": {"0": {"name": "$3 OFF", "description": "Collect 4 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/f397ea80-5a01-11ed-8f14-f918db700f9d.png"}]}, "1": {"name": "$5 OFF", "description": "Collect 6 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/38628ad0-5a02-11ed-8f14-f918db700f9d.png"}]}, "2": {"name": "$10 OFF", "description": "Collect 8 stamps to get a $3 cash voucher. Get 1 stamp with every $6 nett spend in a single transaction when you shop in-app.", "terms": "<!DOCTYPE html><html xmlns=\"http://www.w3.org/1999/xhtml\"><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"/><meta id=\"viewport\" name=\"viewport\"><meta name=\"format-detection\" content=\"telephone=no,address=no,email=no,date=no,url=no\"><script>function sizeCal(){var viewport=document.getElementById('viewport');var frame=document.getElementById('frame');var contentSize;var windowWidth=window.innerWidth;var deviceWidth=window.orientation==0 ? window.screen.width : window.screen.height;var deviceDPI=window.devicePixelRatio ? window.devicePixelRatio : 1;if (navigator.userAgent.indexOf('Android') >=0){viewport.setAttribute(\"content\", \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\");if (deviceWidth !=windowWidth){deviceWidth=deviceWidth / deviceDPI;}}else{var contentWidth='width=' + deviceWidth + 'px, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';viewport.setAttribute(\"content\", contentWidth);}var contentSize=1;if (deviceWidth >=900){contentSize=1.8;}else if (deviceWidth >=480){contentSize=1.5;}else if (deviceWidth >=375){contentSize=1.16;}frame.style.fontSize=contentSize + 'em';};</script><style>@font-face{font-family: MelbourneOld;src: url('fonts/Melbourne.ttf');}@font-face{font-family: MelbourneOld;font-weight: bold;src: url('fonts/Melbourne-Bold.ttf');}@font-face{font-family: Melbourne;src: url('fonts/Melbourne.otf');}@font-face{font-family: Melbourne;font-weight: bold;src: url('fonts/Melbourne-Bold.otf');}*{font-family: \"Melbourne\", \"MelbourneOld\", Arial, sans-serif;-webkit-text-size-adjust: none;margin: 0;padding: 0;}body{font-size: 16px;}#frame{font-size: 1em;margin: 5px 15px 1em;}ul{padding: 0 0 0 1.13em;list-style-type: disc;}li{padding: 0 0 0.32em 0;vertical-align: top;text-align: left;font-size: 1em;line-height: 1.2em;color: #FFFFFF;color: rgba(255,255,255,0.6);}h4{font-weight: bold;font-size: 1em;color: #FFFFFF;color: rgba(255,255,255,0.8);margin: 0 0 0.32em;}p{margin:0}</style><title>Terms & Conditions</title></head><body onLoad=\"sizeCal()\"><div id=\"frame\"><h4>Terms & Conditions</h4><ul><li>Cash voucher can only be used from the next day of the date of issuance.</li><li>Cash voucher is valid for one-time redemption and not exchangeable for cash.</li><li>Cash voucher cannot be used in conjunction with other promotions, membership discounts and third party promotions.</li><li>Femi&#39;s Tea reserves the right to amend the reward without prior notice.&nbsp;</li></ul></div></body></html>", "images": [{"url": "https://s3-ap-southeast-1.amazonaws.com/s3staging.waveo.com/reward/44989c40-5a02-11ed-8f14-f918db700f9d.png"}]}}}}, "default": "en"}, "purgeTime": "2025-07-01T15:59:59.999Z", "when": {"sent": "2023-07-03T15:53:21.260Z", "issued": "2023-07-03T15:53:21.260Z", "received": "2023-07-03T15:53:23.129Z", "notified": null, "viewed": null, "extended": null, "withdrawn": null, "completed": null, "cancelled": null, "recovered": null}, "createdAt": "2023-07-03T15:53:21.693Z", "modifiedAt": "2023-10-03T02:06:59.771Z", "deletedAt": null, "cardId": "64a2eeef2c0073003a54e494", "personId": "64749fdffe89be003a428a90", "channels": [], "_model": "<PERSON><PERSON>"}, "syncUntil": 0}, {"model": "WidgetData", "lastSync": 1696299603423.0, "old": {"id": "64a2eef4712467000079ffa5", "key": "bag", "cardId": "64a2eeef2c0073003a54e494", "personId": "64749fdffe89be003a428a90", "data": {"current": "default", "_syncedAt": "2023-10-03T02:09:05.741Z", "bags": [{"name": "default", "type": "fastdrink", "fulfillment": {"quantity": 1, "unitPrice": 5, "price": 5, "taxes": [], "images": [], "tags": [], "discount": {"amount": 0, "allocations": []}, "status": {}, "kind": "product", "units": 1, "type": "deliver", "recipient": {"pickupTime": "3 Oct, 7pm - 8pm", "postCode": "829704", "fullName": "<PERSON>", "phone": "6590490001", "email": "zhang<PERSON>@waveo.com"}, "spot": {}, "destination": {"city": "Singapore", "street": "247 Ponggol Seventeenth Ave", "country": "SG", "postCode": "829704", "formatted": "247 Ponggol Seventeenth Ave, Singapore 829704", "short": "247 Ponggol Seventeenth Ave", "geo": {"coordinates": [103.9039623, 1.4162192]}, "type": "others", "label": "Office", "id": "ln9ohsjr"}, "origin": {}, "scheduled": {"minTime": "2023-10-03T11:00:00.184Z", "maxTime": "2023-10-03T12:00:00.184Z"}}, "totalPrice": 7.9, "currency": {"code": "SGD", "symbol": "$", "precision": 2}, "taxIncluded": true, "taxes": [{"title": "GST", "rate": 0.08, "price": 0.58}], "endTime": "2023-10-17T02:19:57.699Z", "injectedItems": [], "totalDiscounts": 2, "subtotalPrice": 2.9, "totalTax": 0.58, "items": [{"id": "fresh-milk-blacksugar-0", "variantId": "41097288941700", "productId": "7006305190020", "sku": "fresh-milk-blacksugar", "title": "Black Sugar Fresh Milk", "quantity": 1, "unitPrice": 4.9, "price": 2.9, "taxes": [], "images": ["https://femistea-dev.myshopify.com/cdn/shop/products/27.BlackSugarFreshMilk.png?v=**********"], "tags": ["bestseller", "beverages"], "properties": [], "options": [{"key": "size", "name": "Size", "type": "tagList", "required": true, "max": 1, "min": 1, "values": [{"name": "Standard", "price": 0, "selected": true}, {"name": "Large", "price": 1, "selected": false}]}, {"key": "sweetness", "name": "Sweetness", "type": "slider", "required": true, "values": [{"name": "0%", "value": 0, "price": 0, "selected": false}, {"name": "20%", "value": 0.2, "price": 0, "selected": false}, {"name": "50%", "value": 0.5, "price": 0, "selected": false}, {"name": "100%", "value": 1, "price": 0, "selected": true}]}, {"key": "ice", "name": "Ice level", "type": "radioboxList", "max": 1, "min": 1, "required": true, "values": [{"name": "Regular", "price": 0, "selected": true}, {"name": "Less Ice", "price": 0, "selected": false}, {"name": "No Ice", "price": 1, "selected": false}]}, {"key": "toppings1", "name": "Toppings", "type": "checkboxList", "max": 3, "min": 0, "required": false, "values": [{"name": "Handmade Pearls", "price": 0.5, "selected": false, "image": "http://edm.waveo.com.s3.amazonaws.com/temp/test/top1.jpg"}, {"name": "Red Bean", "price": 0.5, "selected": false, "image": "http://edm.waveo.com.s3.amazonaws.com/temp/test/top2.jpg"}, {"name": "Grass Jelly", "price": 0.5, "selected": false, "image": "http://edm.waveo.com.s3.amazonaws.com/temp/test/top3.jpg"}, {"name": "<PERSON><PERSON>", "price": 1, "selected": false, "image": "http://edm.waveo.com.s3.amazonaws.com/temp/test/top4.jpg"}, {"name": "<PERSON><PERSON><PERSON>", "price": 1, "selected": false, "image": "http://edm.waveo.com.s3.amazonaws.com/temp/test/top5.jpg"}, {"name": "<PERSON><PERSON>", "price": 1, "selected": false, "image": "https://cdn.shopify.com/s/files/1/0605/5700/0836/files/Ub15df50e38f24c9688ec8002534d45cbC.jpg?v=1666865454"}, {"name": "Agar Crystal Ball", "price": 1, "selected": false, "image": "https://cdn.shopify.com/s/files/1/0605/5700/0836/files/6aa9c747-4b8b-47bf-a137-86d042f4eecb.__CR16_0_867_867_PT0_SX300_V1.jpg?v=1666866417"}, {"name": "<PERSON>lo", "price": 1.5, "selected": false, "image": "https://cdn.shopify.com/s/files/1/0605/5700/0836/files/do-you-know-what-the-most-nutrient-dense-snack-is_1.png?v=1666867054"}, {"name": "Cheezy", "price": 1.5, "selected": false, "image": "https://cdn.shopify.com/s/files/1/0605/5700/0836/files/Cheese-foam-poured-on-tea_1.png?v=1666866642"}]}], "discount": {"amount": 2, "allocations": [{"amount": 2, "index": 0}]}, "kind": "product", "units": 1}], "discountsApplied": [{"offerMasterId": "6360eabba9cc37001d6032c2", "offerId": "651b780d79c01f003a8e5233", "name": "$2 OFF", "kind": "fixed", "value": 2, "currency": "SGD", "amount": 2, "use": "combine", "priority": 6, "excludeOffers": [], "targetType": "item", "targetSelection": "all", "allocation": {"method": "across", "limit": 1, "remain": 0}, "entitled": {}}], "modifiedAt": "2023-10-03T02:19:57.761Z", "id": "651b7a4c1c34570000e85e84", "source": {"name": "shop"}}]}, "badge": {"unread": 1, "valid": null}, "createdAt": "2023-07-03T15:53:24.185Z", "modifiedAt": "2023-10-03T02:19:57.762Z", "cardMasterId": "6345469aa56c87001d9107a8", "tenant": {"code": "femistea-sg-test"}, "deletedAt": null, "_model": "WidgetData"}, "new": null}, {"model": "Action", "lastSync": 0, "old": null, "new": {"id": "64a2e489712467000079feb0", "minAppVersion": "4", "object": "engage", "action": "notify", "data": {"title": "Test", "body": "Test Push Content", "data": {"image": "https://s3-us-west-1.amazonaws.com/s3staging.perkd.me/card/a0633d30-6ad5-11ed-9aea-63e6ca3d1403-thumbnail.png"}}, "triggers": {}, "enabled": true, "createdAt": "2023-10-03T02:15:34.130Z", "modifiedAt": "2023-10-03T02:15:34.130Z", "purgeTime": "2024-01-03T02:15:34.130Z", "deletedAt": null, "personId": "64749fdffe89be003a428a90"}}], "createdAt": "2023-10-02T10:08:12.604+0000", "modifiedAt": "2023-10-03T02:12:27.577+0000", "deletedAt": null}