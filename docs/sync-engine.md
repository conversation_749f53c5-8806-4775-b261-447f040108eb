# Sync Engine

The Sync Engine provides a modular architecture for backend services to integrate with the Sync service. This document details the technical implementation and integration requirements.

## Overview

The Sync Engine enables:
- Bi-directional data synchronization between mobile apps and backend services
- Real-time event-driven cache updates
- Conflict resolution with version tracking
- Multi-device data consistency


## Integration Requirements

1. **Model Requirements**
   - Must implement `syncFetch` method for data retrieval
   - Must implement required mixins (`Sync`, `Cache`)
   - Must handle installation context and device management
   - Must emit standardized events for data changes

2. **Service Requirements**
   - Must emit standardized events for CRUD operations
   - Must include required metadata in events
   - Must handle multi-tenant data isolation
   - Must respect sync state tracking

## Core Mixins

### Mixin Dependencies
```mermaid
graph TD
	%% Patterns and Mixins
	syncFetch["syncFetch Method<br/>(required pattern)"]
	Sync["Sync Mixin"]
	Cache["Cache Mixin"]
	Share["Share Mixin"]
	Purge["Purge Mixin"]
	When["When Mixin"]

	%% Dependencies
	syncFetch --> Cache
	Cache --> Share
	Cache --> Purge
	Sync --> Cache
	When --> Cache

	%% Styling
	classDef pattern fill:#6c8ebf,stroke:#6c8ebf,color:white
	classDef core fill:#82b366,stroke:#82b366,color:white
	classDef optional fill:#d79b00,stroke:#d79b00,color:white

	class syncFetch pattern
	class Cache,Sync core
	class Share,Purge,When optional
```

1. **syncFetch Method Pattern**
   - Foundational implementation pattern (not a formal mixin)
   - Required method that models must implement
   - Standardizes data fetch operations
   - Handles model-specific data transformation
   - Required prerequisite for Cache mixin

2. **Sync Mixin**
   - Handles event-driven cache updates
   - Manages sync state and operations
   - Processes sync requests and responses
   - Handles incremental updates
   - Provides sync API endpoints:
     - `/:model/app/sync` - Model-specific bi-directional sync
     - `/:model/app/fetch` - Targeted object retrieval

3. **Cache Mixin**
   - Manages Redis-based caching
   - Requires `syncFetch` method implementation
   - Maintains sync consistency
   - Provides cache invalidation
   - Integrates with change logging
   - Handles CRUD event tracking

4. **Share Mixin**
   - Manages sharing functionality
   - Maintains sync state for shared objects
   - Emits sync-compatible events
   - Formats responses for sync
   - Integrates with cache
   - Handles multi-device synchronization

5. **Purge Mixin**
   - Manages data lifecycle
   - Handles automatic cleanup
   - Integrates with Cache mixin
   - Maintains sync consistency
   - Emits purge events
   - Ensures proper data cleanup

6. **When Mixin**
   - Manages timing-based states
   - Tracks state changes
   - Formats sync responses
   - Emits timing events
   - Maintains state consistency
   - Integrates with Event mixin


## Event-Driven Architecture

### Cache Update Flow
```mermaid
graph TD
	%% Event Sources
	Event["Model Event<br/>{service}.{model}.{operation}"]

	%% Processing Steps
	MetaCheck["Check Event Metadata<br/>- personId<br/>- timestamp<br/>- delta<br/>- context"]
	StateCheck["Check State<br/>- when states<br/>- sync states"]
	Cache["Update Redis Cache<br/>- sync consistency<br/>- version tracking"]
	Log["Write to Change Log<br/>- track changes<br/>- sync history"]
	Notify["Notify Connected Clients<br/>- emit events<br/>- sync updates"]

	%% Flow
	Event --> MetaCheck
	MetaCheck --> StateCheck
	StateCheck --> Cache
	Cache --> Log
	Log --> Notify

	%% Error Handling & Recovery
	MetaCheck -->|"Invalid"| ErrorLog["Log Error<br/>- metadata errors"]
	StateCheck -->|"Invalid"| ErrorLog
	Cache -->|"Failed"| RetryCache["Retry Cache<br/>- recovery logic"]
	RetryCache -->|"Max Retries"| ErrorLog
	RetryCache -->|"Success"| Log

	%% Styling
	classDef event fill:#6c8ebf,stroke:#6c8ebf,color:white
	classDef process fill:#82b366,stroke:#82b366,color:white
	classDef error fill:#b85450,stroke:#b85450,color:white
	classDef retry fill:#d79b00,stroke:#d79b00,color:white

	class Event event
	class MetaCheck,StateCheck,Cache,Log,Notify process
	class ErrorLog error
	class RetryCache retry
```

1. **Event Pattern**
   - Format: `{service}.{model}.{operation}`
   - Operations: `created`, `updated`, `deleted`
   - Example: `card.offer.created`

2. **Event Metadata**
   - `personId` - Owner of the data
   - `timestamp` - Operation timestamp
   - `delta` - Changes in the update
   - `context` - Additional operation context

3. **Cache Updates**
   - Events trigger automatic cache updates
   - Updates maintain in-memory cache
   - Updates write to change logs
   - Updates respect tenant isolation

## Implementation Guide

1. **Adding Sync to a Model**
   ```javascript
   // 1. Add required mixins in correct order
   Model.mixin('Sync', {})
   Model.mixin('Cache', {})

   // 2. Implement syncFetch method
   Model.syncFetch = async function(personId, timestamp, ids) {
     // Implementation
   }

   // 3. Emit sync events
   Model.emit(`${service}.${model}.created`, {
     personId,
     timestamp,
     // ... other metadata
   })
   ```

2. **Cache Configuration**
   ```javascript
   // Configure cache options
   Model.mixin('Cache', {
     service: 'customService',  // Optional service name
     // ... other cache options
   })
   ```



## Model-Specific Sync API Endpoints

**POST** `/:model/app/sync`

Description:
- Model-specific sync endpoint
- Available on models with Sync mixin
- Handles conflict resolution for specific model
- Supports version-based sync

Request:
- Body:
  - `objectsUp` (array, required): Array of objects to sync
  - `options` (object, optional): Sync options
    - `syncUntil` (number): Sync cutoff timestamp
    - `fetchLimit` (number): Maximum objects to sync

Response:
- `objects` (array): Updated/new objects from server
- `syncUntil` (number): Server sync timestamp
- `partial` (boolean): Whether sync is partial

**GET** `/:model/app/fetch`

Description:
- Targeted object retrieval for specific model
- Optimized for selective sync

Request:
- Query Parameters:
  - `ids` (array, required): Array of object IDs to fetch
  - `options` (object, optional): Fetch options
    - `personId` (string): Person ID for scoping the fetch

Response:
- `sync` (object): Object containing fetched objects

## Best Practices

1. **Mixin Order**
   - Implement `syncFetch` method before adding `Cache` mixin
   - Add `Share` after `Cache` if needed
   - Add `Purge` last in the chain

2. **Event Handling**
   - Emit events after successful operations
   - Include all required metadata
   - Handle event failures gracefully
   - Log event processing errors

3. **Cache Management**
   - Implement proper cache invalidation
   - Handle cache miss scenarios
   - Monitor cache size limits
   - Implement cache cleanup strategies

4. **Error Handling**
   - Implement proper error recovery
   - Log sync failures
   - Handle network issues
   - Maintain data consistency

## Model-Specific Implementation Examples

### Action Model

```javascript
Action.syncFetch = async function(personId, last) {
  const token = getToken(),
    lastSync = last && last.toISOString(),
    [ actions ] = await Action.syncFetchRest(personId, lastSync, token),
    instances = actions.map(a => new Action(a));
  return instances;
}
```

### Card Model

```javascript
Card.syncFetch = async function(personId, last) {
  const filter = {
    where: {
      personId: personId,
      deletedAt: null
    }
  };

  // Add timestamp filter if provided
  if (last) {
    filter.where.or = [
      { createdAt: { gt: last } },
      { modifiedAt: { gt: last } }
    ];
  }

  return Card.find(filter);
}
```

## Streaming Responses

The sync engine uses streaming responses for efficient data transfer, especially for large datasets:

```javascript
// Create a stream for the response
const stream = sync.streams.create(res, install);

// Begin the stream with appropriate headers
stream.begin(head);

// Stream objects as they become available
stream.objects(objects);

// End the stream
stream.end(tail, syncUntil);
```

This approach allows the server to send data incrementally rather than building a complete response in memory.

## Legacy Support (Pre-7.0)

The sync engine maintains backward compatibility with pre-7.0 app versions:

```javascript
const legacy = isPre7app(installation);
if (legacy) {
  // Apply legacy compatibility transformations
  const index = objectsUp.findIndex(o => o._model === 'Preference');
  if (index !== -1) objectsUp[index]._model = 'AccountSetting';

  // Use legacy response format
  const head = '{\'objects\':[';
  // ...
}
```

Legacy support includes:
- Automatic model name conversion (e.g., Preference → AccountSetting)
- Legacy response format support
- Compatibility with older sync protocols

## Error Handling

### Event Error Handling

```javascript
appOn(Event.object.created, async evt => {
  try {
    const { sync } = appModule(SYNC),
      { id, personId = id, createdAt, purgeTime } = evt;
    await sync.changes.objectCreated(personId, model, String(id), createdAt, purgeTime);
  }
  catch (err) {
    console.error('[Cache] object.created event:', { err, evt });
  }
});
```

### Sync Operation Error Handling

```javascript
try {
  const result = await sync.fetch(after, max, { ...options });
  sync.response(result, options.install, res);
} catch (error) {
  // Handle sync operation errors
  console.error('[Sync] fetch error:', error);
  res.status(500).send({ error: 'Sync operation failed' });
}
```

### Best Practices for Error Handling

- Log detailed error information
- Implement retry mechanisms for transient failures
- Maintain data consistency during error recovery
- Provide meaningful error responses to clients
- Use try-catch blocks around all async operations

## Troubleshooting

1. **Common Issues**
   - Missing `syncFetch` method implementation
   - Missing required mixins
   - Incorrect mixin order
   - Missing event metadata
   - Cache inconsistencies

2. **Debugging**
   - Enable sync logging
   - Monitor event processing
   - Check cache state
   - Verify event emissions
   - Inspect Redis cache contents