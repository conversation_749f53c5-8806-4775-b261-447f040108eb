# Sync Service API Documentation

## Overview

The Sync Service provides APIs for data synchronization and caching for the Perkd mobile app.

## API Endpoints

### Authentication
Required headers:
- `tenant-code`: Tenant identifier
- `Authorization`: Bearer token (if required)

### Sync APIs

**POST** `/Cache/app/sync`

Description:
- Main bi-directional sync endpoint for all model types
- Handles conflict resolution across models
- Supports streaming responses for efficient data transfer
- Compatible with both legacy (pre-7.0) and current app versions

Request:
- Body:
  - `objectsUp` (array, required): Array of objects to sync
    - Each object must include: `id`, `_model`, and other model-specific properties
    - Supported models: `Action`, `Card`, `Offer`, `Reward`, `Message`, `Person`, `Place`, `AccountSetting`, `AppEvent`, `WidgetData`
  - `syncUntil` (number, optional): Timestamp for sync cutoff
  - `limit` (number, optional): Maximum number of objects to sync
  - `background` (boolean, optional): Whether to run sync in background
  - `options` (object, optional): Legacy sync options (pre-7.0)
    - `syncUntil` (number): Override cache sync timestamp
    - `fetchLimit` (number): Maximum number of objects to sync down

Response:
- `objects` (array): Updated/new objects from server
- `syncUntil` (number): Server sync timestamp
- `partial` (boolean): Whether sync is partial

### Rate Limiting
- Request body size: 10mb max
- URL encoded limit: 100kb
- Rate limits per endpoint:
  - Sync: 60 requests per minute
  - Message sending: 30 requests per minute
